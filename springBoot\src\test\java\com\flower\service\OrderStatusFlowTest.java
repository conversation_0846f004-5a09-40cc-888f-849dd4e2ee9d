package com.flower.service;

import com.flower.entity.Order;
import com.flower.service.impl.OrderServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 订单状态流程测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class OrderStatusFlowTest {

    /**
     * 测试正确的管理端订单状态流程：
     * 2(已下单) -> 3(配送中) -> 1(待付款) -> 4(已完成)
     */
    @Test
    public void testCorrectOrderStatusFlow() {
        // 创建一个模拟订单
        Order order = new Order();
        order.setId(1L);
        order.setUserId(1L);
        order.setStatus(2); // 已下单
        order.setPaymentStatus(0); // 未支付

        // 验证初始状态
        assertEquals(2, order.getStatus()); // 已下单
        assertEquals(0, order.getPaymentStatus()); // 未支付

        // 测试状态转换逻辑
        // 1. 已下单 -> 配送中
        assertTrue(isValidStatusTransition(2, 0, 3));

        // 2. 配送中 -> 待付款
        assertTrue(isValidStatusTransition(3, 0, 1));

        // 3. 待付款 -> 已完成
        assertTrue(isValidStatusTransition(1, 0, 4));

        // 测试无效的状态转换
        // 不能跳过状态
        assertFalse(isValidStatusTransition(2, 0, 1)); // 已下单不能直接到待付款
        assertFalse(isValidStatusTransition(2, 0, 4)); // 已下单不能直接到已完成
        assertFalse(isValidStatusTransition(3, 0, 4)); // 配送中不能直接到已完成

        // 已完成的订单只能取消
        assertFalse(isValidStatusTransition(4, 1, 2)); // 已完成不能回到已下单
        assertTrue(isValidStatusTransition(4, 1, 5)); // 已完成可以取消

        // 已取消的订单不能再更改
        assertFalse(isValidStatusTransition(5, 0, 2)); // 已取消不能改为其他状态
    }
    
    /**
     * 测试付款状态的正确处理
     */
    @Test
    public void testPaymentStatusHandling() {
        // 在确认付款前，付款状态应该为未支付
        assertTrue(isValidStatusTransition(3, 0, 1)); // 配送中，未支付 -> 待付款

        // 确认付款后，付款状态应该为已支付
        assertTrue(isValidStatusTransition(1, 0, 4)); // 待付款，未支付 -> 已完成（确认付款）

        // 测试各个阶段都可以取消
        assertTrue(isValidStatusTransition(2, 0, 5)); // 已下单可以取消
        assertTrue(isValidStatusTransition(3, 0, 5)); // 配送中可以取消
        assertTrue(isValidStatusTransition(1, 0, 5)); // 待付款可以取消
    }
    
    /**
     * 模拟状态转换验证逻辑（基于AdminController中的实现）
     */
    private boolean isValidStatusTransition(Integer currentStatus, Integer paymentStatus, Integer newStatus) {
        // 已取消的订单不能再更改状态
        if (currentStatus == 5) {
            return false;
        }

        // 已完成的订单只能取消（特殊情况）
        if (currentStatus == 4 && newStatus != 5) {
            return false;
        }

        // 管理端订单状态流程（基于货到付款模式）：
        // 2(已下单) -> 3(配送中), 5(已取消)
        // 3(配送中) -> 1(待付款), 5(已取消)
        // 1(待付款) -> 4(已完成), 5(已取消)
        // 4(已完成) -> 5(已取消)

        switch (currentStatus) {
            case 2: // 已下单
                return newStatus == 3 || newStatus == 5;
            case 3: // 配送中
                return newStatus == 1 || newStatus == 5;
            case 1: // 待付款
                return newStatus == 4 || newStatus == 5;
            case 4: // 已完成
                return newStatus == 5; // 只能取消
            default:
                return false;
        }
    }
    
    /**
     * 测试订单状态文本
     */
    @Test
    public void testOrderStatusText() {
        assertEquals("待付款", getOrderStatusText(1));
        assertEquals("已下单", getOrderStatusText(2));
        assertEquals("配送中", getOrderStatusText(3));
        assertEquals("已完成", getOrderStatusText(4));
        assertEquals("已取消", getOrderStatusText(5));
        assertEquals("未知状态", getOrderStatusText(99));
    }

    private String getOrderStatusText(Integer status) {
        switch (status) {
            case 1: return "待付款";
            case 2: return "已下单";
            case 3: return "配送中";
            case 4: return "已完成";
            case 5: return "已取消";
            default: return "未知状态";
        }
    }
}
