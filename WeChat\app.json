{"pages": ["pages/index/index", "pages/category/category", "pages/category-products/category-products", "pages/cart/cart", "pages/profile/profile", "pages/detail/detail", "pages/search/search", "pages/order/order", "pages/order-detail/order-detail", "pages/favorites/favorites", "pages/login/login", "pages/user-edit/user-edit", "pages/address-list/address-list", "pages/address-edit/address-edit", "pages/order-confirm/order-confirm", "pages/order-list/order-list"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#ff6b9d", "navigationBarTitleText": "花语小铺", "navigationBarTextStyle": "white", "backgroundColor": "#f8f8f8"}, "tabBar": {"color": "#999999", "selectedColor": "#ff6b9d", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "/images/布偶猫.png", "selectedIconPath": "/images/布偶猫.png"}, {"pagePath": "pages/category/category", "text": "分类", "iconPath": "/images/柴犬.png", "selectedIconPath": "/images/柴犬.png"}, {"pagePath": "pages/cart/cart", "text": "购物车", "iconPath": "/images/仓鼠.png", "selectedIconPath": "/images/仓鼠.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "/images/可达鸭.png", "selectedIconPath": "/images/可达鸭.png"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": true, "sitemapLocation": "sitemap.json"}