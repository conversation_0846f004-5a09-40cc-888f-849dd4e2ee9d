package com.flower.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.common.Result;
import com.flower.entity.*;
import com.flower.vo.FlowerVO;
import com.flower.mapper.*;
import com.flower.service.AdminService;
import com.flower.service.FileUploadService;
import com.flower.service.OrderService;
import com.flower.service.PriceCategoryService;
import com.flower.util.JwtUtil;
import com.flower.util.PasswordUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin")
@CrossOrigin(originPatterns = "*", allowedHeaders = "*", allowCredentials = "true")
public class AdminController {



    @Autowired
    private AdminService adminService;

    @Autowired
    private PriceCategoryService priceCategoryService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private AdminUserMapper adminUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FlowerMapper flowerMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderItemMapper orderItemMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private FlowerReviewMapper flowerReviewMapper;

    @Autowired
    private RegionMapper regionMapper;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> params, HttpServletRequest request) {
        try {
            String username = params.get("username");
            String password = params.get("password");
            
            if (username == null || username.trim().isEmpty()) {
                return Result.paramError("用户名不能为空");
            }
            
            if (password == null || password.trim().isEmpty()) {
                return Result.paramError("密码不能为空");
            }
            
            // 登录验证
            AdminUser adminUser = adminService.login(username, password);
            
            // 生成token
            String token = adminService.generateToken(adminUser);
            
            // 更新最后登录信息
            String ipAddress = getClientIpAddress(request);
            adminService.updateLastLogin(adminUser.getId(), ipAddress);
            
            // 记录登录日志
            adminService.logAction(adminUser.getId(), adminUser.getUsername(), "LOGIN", 
                                 "ADMIN", adminUser.getId().toString(), "管理员登录", 
                                 ipAddress, request.getHeader("User-Agent"));
            
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", adminUser);
            
            return Result.success("登录成功", result);
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 验证token
     */
    @GetMapping("/verify")
    public Result<AdminUser> verifyToken(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("Token不能为空");
            }
            
            AdminUser adminUser = adminService.verifyToken(token);
            return Result.success("Token验证成功", adminUser);
        } catch (Exception e) {
            log.error("Token验证失败", e);
            return Result.error("Token验证失败: " + e.getMessage());
        }
    }

    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    public Result<String> logout(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token != null) {
                AdminUser adminUser = adminService.verifyToken(token);
                
                // 记录登出日志
                String ipAddress = getClientIpAddress(request);
                adminService.logAction(adminUser.getId(), adminUser.getUsername(), "LOGOUT", 
                                     "ADMIN", adminUser.getId().toString(), "管理员登出", 
                                     ipAddress, request.getHeader("User-Agent"));
            }
            
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("管理员登出失败", e);
            return Result.success("登出成功"); // 即使出错也返回成功，因为前端会清除token
        }
    }

    /**
     * 获取统计数据
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 用户总数
            long userCount = userMapper.selectCount(null);
            stats.put("userCount", userCount);
            
            // 商品总数
            long flowerCount = flowerMapper.selectCount(null);
            stats.put("flowerCount", flowerCount);
            
            // 订单总数
            long orderCount = orderMapper.selectCount(null);
            stats.put("orderCount", orderCount);
            
            // 总销售额（这里需要根据实际订单表结构计算）
            // 假设订单表有totalAmount字段
            BigDecimal totalSales = BigDecimal.ZERO;
            List<Order> orders = orderMapper.selectList(null);
            for (Order order : orders) {
                if (order.getTotalAmount() != null) {
                    totalSales = totalSales.add(order.getTotalAmount());
                }
            }
            stats.put("totalSales", totalSales);
            
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取图表数据
     */
    @GetMapping("/chart/{type}")
    public Result<Map<String, Object>> getChartData(@PathVariable String type) {
        try {
            Map<String, Object> chartData = new HashMap<>();
            
            switch (type) {
                case "order-week":
                case "order-month":
                    // 订单趋势数据
                    int days = type.equals("order-week") ? 7 : 30;
                    List<String> dates = new ArrayList<>();
                    List<Integer> counts = new ArrayList<>();
                    
                    LocalDateTime endDate = LocalDateTime.now();
                    LocalDateTime startDate = endDate.minusDays(days - 1);
                    
                    for (int i = 0; i < days; i++) {
                        LocalDateTime currentDate = startDate.plusDays(i);
                        String dateStr = currentDate.format(DateTimeFormatter.ofPattern("MM-dd"));
                        dates.add(dateStr);
                        
                        // 查询当天订单数量
                        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
                        wrapper.ge(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay());
                        wrapper.lt(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay().plusDays(1));
                        long dayCount = orderMapper.selectCount(wrapper);
                        counts.add((int) dayCount);
                    }
                    
                    chartData.put("dates", dates);
                    chartData.put("counts", counts);
                    break;
                    
                case "category-sales":
                    // 分类销量数据
                    List<Category> categories = categoryMapper.selectList(null);
                    List<Map<String, Object>> categoryData = new ArrayList<>();
                    
                    for (Category category : categories) {
                        // 统计该分类下商品的销量
                        LambdaQueryWrapper<Flower> flowerWrapper = new LambdaQueryWrapper<>();
                        flowerWrapper.eq(Flower::getCategoryId, category.getId());
                        List<Flower> flowers = flowerMapper.selectList(flowerWrapper);
                        
                        int totalSales = 0;
                        for (Flower flower : flowers) {
                            if (flower.getSalesCount() != null) {
                                totalSales += flower.getSalesCount();
                            }
                        }
                        
                        if (totalSales > 0) {
                            Map<String, Object> item = new HashMap<>();
                            item.put("name", category.getName());
                            item.put("value", totalSales);
                            categoryData.add(item);
                        }
                    }
                    
                    chartData.put("categories", categoryData);
                    break;

                case "sales-trend":
                    // 销售额趋势数据
                    List<String> salesDates = new ArrayList<>();
                    List<BigDecimal> salesAmounts = new ArrayList<>();

                    LocalDateTime salesEndDate = LocalDateTime.now();
                    LocalDateTime salesStartDate = salesEndDate.minusDays(29);

                    for (int i = 0; i < 30; i++) {
                        LocalDateTime currentDate = salesStartDate.plusDays(i);
                        String dateStr = currentDate.format(DateTimeFormatter.ofPattern("MM-dd"));
                        salesDates.add(dateStr);

                        // 查询当天销售额
                        LambdaQueryWrapper<Order> salesWrapper = new LambdaQueryWrapper<>();
                        salesWrapper.ge(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay());
                        salesWrapper.lt(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay().plusDays(1));
                        salesWrapper.in(Order::getStatus, Arrays.asList(2, 3, 4)); // 已支付、已发货、已完成

                        List<Order> dayOrders = orderMapper.selectList(salesWrapper);
                        BigDecimal dayAmount = BigDecimal.ZERO;
                        for (Order order : dayOrders) {
                            if (order.getTotalAmount() != null) {
                                dayAmount = dayAmount.add(order.getTotalAmount());
                            }
                        }
                        salesAmounts.add(dayAmount);
                    }

                    chartData.put("dates", salesDates);
                    chartData.put("amounts", salesAmounts);
                    break;

                case "user-growth":
                    // 用户增长趋势数据
                    List<String> userDates = new ArrayList<>();
                    List<Integer> userCounts = new ArrayList<>();

                    LocalDateTime userEndDate = LocalDateTime.now();
                    LocalDateTime userStartDate = userEndDate.minusDays(29);

                    for (int i = 0; i < 30; i++) {
                        LocalDateTime currentDate = userStartDate.plusDays(i);
                        String dateStr = currentDate.format(DateTimeFormatter.ofPattern("MM-dd"));
                        userDates.add(dateStr);

                        // 查询截止到当天的用户总数
                        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
                        userWrapper.le(User::getCreatedAt, currentDate.toLocalDate().atStartOfDay().plusDays(1));
                        long totalUsers = userMapper.selectCount(userWrapper);
                        userCounts.add((int) totalUsers);
                    }

                    chartData.put("dates", userDates);
                    chartData.put("counts", userCounts);
                    break;

                case "stock-status":
                    // 商品库存状态分布
                    List<Map<String, Object>> stockData = new ArrayList<>();

                    // 缺货商品
                    LambdaQueryWrapper<Flower> stockWrapper1 = new LambdaQueryWrapper<>();
                    stockWrapper1.eq(Flower::getStockQuantity, 0);
                    long outOfStock = flowerMapper.selectCount(stockWrapper1);
                    if (outOfStock > 0) {
                        Map<String, Object> item1 = new HashMap<>();
                        item1.put("name", "缺货");
                        item1.put("value", outOfStock);
                        stockData.add(item1);
                    }

                    // 库存不足(1-10)
                    LambdaQueryWrapper<Flower> stockWrapper2 = new LambdaQueryWrapper<>();
                    stockWrapper2.gt(Flower::getStockQuantity, 0);
                    stockWrapper2.le(Flower::getStockQuantity, 10);
                    long lowStock = flowerMapper.selectCount(stockWrapper2);
                    if (lowStock > 0) {
                        Map<String, Object> item2 = new HashMap<>();
                        item2.put("name", "库存不足");
                        item2.put("value", lowStock);
                        stockData.add(item2);
                    }

                    // 库存正常(11-50)
                    LambdaQueryWrapper<Flower> stockWrapper3 = new LambdaQueryWrapper<>();
                    stockWrapper3.gt(Flower::getStockQuantity, 10);
                    stockWrapper3.le(Flower::getStockQuantity, 50);
                    long normalStock = flowerMapper.selectCount(stockWrapper3);
                    if (normalStock > 0) {
                        Map<String, Object> item3 = new HashMap<>();
                        item3.put("name", "库存正常");
                        item3.put("value", normalStock);
                        stockData.add(item3);
                    }

                    // 库存充足(50+)
                    LambdaQueryWrapper<Flower> stockWrapper4 = new LambdaQueryWrapper<>();
                    stockWrapper4.gt(Flower::getStockQuantity, 50);
                    long highStock = flowerMapper.selectCount(stockWrapper4);
                    if (highStock > 0) {
                        Map<String, Object> item4 = new HashMap<>();
                        item4.put("name", "库存充足");
                        item4.put("value", highStock);
                        stockData.add(item4);
                    }

                    chartData.put("stockStatus", stockData);
                    break;

                case "order-status":
                    // 订单状态分布
                    List<Map<String, Object>> orderStatusData = new ArrayList<>();

                    String[] statusNames = {"待付款", "已付款", "已发货", "已完成", "已取消"};
                    for (int status = 1; status <= 5; status++) {
                        LambdaQueryWrapper<Order> statusWrapper = new LambdaQueryWrapper<>();
                        statusWrapper.eq(Order::getStatus, status);
                        long statusCount = orderMapper.selectCount(statusWrapper);

                        if (statusCount > 0) {
                            Map<String, Object> item = new HashMap<>();
                            item.put("name", statusNames[status - 1]);
                            item.put("value", statusCount);
                            orderStatusData.add(item);
                        }
                    }

                    chartData.put("orderStatus", orderStatusData);
                    break;

                case "hot-products":
                    // 热销商品排行榜
                    LambdaQueryWrapper<Flower> hotWrapper = new LambdaQueryWrapper<>();
                    hotWrapper.orderByDesc(Flower::getSalesCount);
                    hotWrapper.last("LIMIT 10");
                    List<Flower> hotFlowers = flowerMapper.selectList(hotWrapper);

                    List<Map<String, Object>> hotProductsData = new ArrayList<>();
                    for (Flower flower : hotFlowers) {
                        if (flower.getSalesCount() != null && flower.getSalesCount() > 0) {
                            Map<String, Object> item = new HashMap<>();
                            item.put("name", flower.getName());
                            item.put("sales", flower.getSalesCount());
                            item.put("price", flower.getPrice());
                            hotProductsData.add(item);
                        }
                    }

                    chartData.put("hotProducts", hotProductsData);
                    break;

                default:
                    return Result.error("不支持的图表类型");
            }

            return Result.success(chartData);
        } catch (Exception e) {
            log.error("获取图表数据失败", e);
            return Result.error("获取图表数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    public Result<PageResult<User>> getUsers(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status) {
        try {
            Page<User> page = new Page<>(current, size);
            LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.and(w -> w.like(User::getNickname, keyword)
                       .or()
                       .like(User::getPhone, keyword));
            }

            // 状态筛选
            if (status != null) {
                wrapper.eq(User::getStatus, status);
            }
            
            wrapper.orderByDesc(User::getCreatedAt);
            
            Page<User> result = userMapper.selectPage(page, wrapper);
            
            PageResult<User> pageResult = new PageResult<>();
            pageResult.setRecords(result.getRecords());
            pageResult.setTotal(result.getTotal());
            pageResult.setCurrent(result.getCurrent());
            pageResult.setSize(result.getSize());
            pageResult.setPages(result.getPages());
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/users/{id}")
    public Result<User> getUserDetail(@PathVariable Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.notFound("用户不存在");
            }
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户详情失败", e);
            return Result.error("获取用户详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/users/{id}")
    public Result<User> updateUser(@PathVariable Long id, @RequestBody User user) {
        try {
            User existingUser = userMapper.selectById(id);
            if (existingUser == null) {
                return Result.notFound("用户不存在");
            }

            // 更新用户信息
            user.setId(id);
            user.setUpdatedAt(LocalDateTime.now());

            // 保留原有的创建时间、openid等重要字段
            user.setCreatedAt(existingUser.getCreatedAt());
            user.setOpenid(existingUser.getOpenid());
            user.setUnionid(existingUser.getUnionid());
            user.setSessionKey(existingUser.getSessionKey());

            int result = userMapper.updateById(user);
            if (result > 0) {
                User updatedUser = userMapper.selectById(id);
                return Result.success("用户信息更新成功", updatedUser);
            } else {
                return Result.error("更新用户信息失败");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error("更新用户信息失败: " + e.getMessage());
        }
    }



    /**
     * CORS测试接口
     */
    @GetMapping("/test-cors")
    public Result<String> testCors() {
        return Result.success("CORS配置正常");
    }

    /**
     * 获取后端用户列表
     */
    @GetMapping("/admin-users")
    public Result<PageResult<AdminUser>> getAdminUsers(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String role) {
        try {
            Page<AdminUser> page = new Page<>(current, size);
            LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.and(w -> w.like(AdminUser::getUsername, keyword)
                       .or()
                       .like(AdminUser::getEmail, keyword)
                       .or()
                       .like(AdminUser::getRealName, keyword));
            }

            // 状态筛选
            if (status != null) {
                wrapper.eq(AdminUser::getStatus, status);
            }

            // 角色筛选
            if (role != null && !role.trim().isEmpty()) {
                wrapper.eq(AdminUser::getRole, role);
            }

            // 按ID正序排列，保持数据顺序的一致性
            wrapper.orderByAsc(AdminUser::getId);

            Page<AdminUser> result = adminUserMapper.selectPage(page, wrapper);

            PageResult<AdminUser> pageResult = new PageResult<>();
            pageResult.setRecords(result.getRecords());
            pageResult.setTotal(result.getTotal());
            pageResult.setCurrent(result.getCurrent());
            pageResult.setSize(result.getSize());
            pageResult.setPages(result.getPages());

            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取后端用户列表失败", e);
            return Result.error("获取后端用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取后端用户详情
     */
    @GetMapping("/admin-users/{id}")
    public Result<AdminUser> getAdminUserDetail(@PathVariable Long id) {
        try {
            AdminUser adminUser = adminUserMapper.selectById(id);
            if (adminUser == null) {
                return Result.notFound("用户不存在");
            }
            return Result.success(adminUser);
        } catch (Exception e) {
            log.error("获取后端用户详情失败", e);
            return Result.error("获取后端用户详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建后端用户
     */
    @PostMapping("/admin-users")
    public Result<AdminUser> createAdminUser(@RequestBody AdminUser adminUser) {
        try {
            // 验证必填字段
            if (adminUser.getUsername() == null || adminUser.getUsername().trim().isEmpty()) {
                return Result.paramError("用户名不能为空");
            }
            if (adminUser.getEmail() == null || adminUser.getEmail().trim().isEmpty()) {
                return Result.paramError("邮箱不能为空");
            }
            if (adminUser.getPassword() == null || adminUser.getPassword().trim().isEmpty()) {
                return Result.paramError("密码不能为空");
            }
            if (adminUser.getRealName() == null || adminUser.getRealName().trim().isEmpty()) {
                return Result.paramError("真实姓名不能为空");
            }
            if (adminUser.getRole() == null || adminUser.getRole().trim().isEmpty()) {
                return Result.paramError("角色不能为空");
            }

            // 检查用户名是否重复
            LambdaQueryWrapper<AdminUser> usernameWrapper = new LambdaQueryWrapper<>();
            usernameWrapper.eq(AdminUser::getUsername, adminUser.getUsername().trim());
            AdminUser existingByUsername = adminUserMapper.selectOne(usernameWrapper);
            if (existingByUsername != null) {
                return Result.paramError("用户名已存在");
            }

            // 检查邮箱是否重复
            LambdaQueryWrapper<AdminUser> emailWrapper = new LambdaQueryWrapper<>();
            emailWrapper.eq(AdminUser::getEmail, adminUser.getEmail().trim());
            AdminUser existingByEmail = adminUserMapper.selectOne(emailWrapper);
            if (existingByEmail != null) {
                return Result.paramError("邮箱已存在");
            }

            // 设置默认值
            adminUser.setUsername(adminUser.getUsername().trim());
            adminUser.setEmail(adminUser.getEmail().trim());
            adminUser.setRealName(adminUser.getRealName().trim());
            adminUser.setRole(adminUser.getRole().trim());

            // 加密密码
            adminUser.setPassword(PasswordUtil.encode(adminUser.getPassword()));

            if (adminUser.getStatus() == null) {
                adminUser.setStatus(1);
            }

            adminUser.setCreatedAt(LocalDateTime.now());
            adminUser.setUpdatedAt(LocalDateTime.now());

            adminUserMapper.insert(adminUser);

            // 清除密码字段再返回
            adminUser.setPassword(null);

            return Result.success("后端用户创建成功", adminUser);
        } catch (Exception e) {
            log.error("创建后端用户失败", e);
            return Result.error("创建后端用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新后端用户
     */
    @PutMapping("/admin-users/{id}")
    public Result<AdminUser> updateAdminUser(@PathVariable Long id, @RequestBody AdminUser adminUser) {
        try {
            AdminUser existing = adminUserMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("用户不存在");
            }

            // 验证必填字段
            if (adminUser.getUsername() == null || adminUser.getUsername().trim().isEmpty()) {
                return Result.paramError("用户名不能为空");
            }
            if (adminUser.getEmail() == null || adminUser.getEmail().trim().isEmpty()) {
                return Result.paramError("邮箱不能为空");
            }
            if (adminUser.getRealName() == null || adminUser.getRealName().trim().isEmpty()) {
                return Result.paramError("真实姓名不能为空");
            }
            if (adminUser.getRole() == null || adminUser.getRole().trim().isEmpty()) {
                return Result.paramError("角色不能为空");
            }

            // 检查用户名是否重复（排除自己）
            LambdaQueryWrapper<AdminUser> usernameWrapper = new LambdaQueryWrapper<>();
            usernameWrapper.eq(AdminUser::getUsername, adminUser.getUsername().trim())
                           .ne(AdminUser::getId, id);
            AdminUser duplicateUsername = adminUserMapper.selectOne(usernameWrapper);
            if (duplicateUsername != null) {
                return Result.paramError("用户名已存在");
            }

            // 检查邮箱是否重复（排除自己）
            LambdaQueryWrapper<AdminUser> emailWrapper = new LambdaQueryWrapper<>();
            emailWrapper.eq(AdminUser::getEmail, adminUser.getEmail().trim())
                        .ne(AdminUser::getId, id);
            AdminUser duplicateEmail = adminUserMapper.selectOne(emailWrapper);
            if (duplicateEmail != null) {
                return Result.paramError("邮箱已存在");
            }

            // 更新字段
            existing.setUsername(adminUser.getUsername().trim());
            existing.setEmail(adminUser.getEmail().trim());
            existing.setRealName(adminUser.getRealName().trim());
            existing.setRole(adminUser.getRole().trim());

            if (adminUser.getPhone() != null) {
                existing.setPhone(adminUser.getPhone().trim());
            }
            if (adminUser.getAvatar() != null) {
                existing.setAvatar(adminUser.getAvatar());
            }
            if (adminUser.getRemark() != null) {
                existing.setRemark(adminUser.getRemark());
            }
            if (adminUser.getStatus() != null) {
                existing.setStatus(adminUser.getStatus());
            }

            // 如果提供了新密码，则更新密码
            if (adminUser.getPassword() != null && !adminUser.getPassword().trim().isEmpty()) {
                existing.setPassword(PasswordUtil.encode(adminUser.getPassword()));
            }

            existing.setUpdatedAt(LocalDateTime.now());

            adminUserMapper.updateById(existing);

            // 清除密码字段再返回
            existing.setPassword(null);

            return Result.success("后端用户更新成功", existing);
        } catch (Exception e) {
            log.error("更新后端用户失败", e);
            return Result.error("更新后端用户失败: " + e.getMessage());
        }
    }

    /**
     * 更新后端用户状态
     */
    @PutMapping("/admin-users/{id}/status")
    public Result<String> updateAdminUserStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            AdminUser adminUser = adminUserMapper.selectById(id);
            if (adminUser == null) {
                return Result.notFound("用户不存在");
            }

            adminUser.setStatus(status);
            adminUser.setUpdatedAt(LocalDateTime.now());
            adminUserMapper.updateById(adminUser);

            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success("用户状态已更新为" + statusText);
        } catch (Exception e) {
            log.error("更新后端用户状态失败", e);
            return Result.error("更新后端用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除后端用户
     */
    @DeleteMapping("/admin-users/{id}")
    public Result<String> deleteAdminUser(@PathVariable Long id) {
        try {
            AdminUser adminUser = adminUserMapper.selectById(id);
            if (adminUser == null) {
                return Result.notFound("用户不存在");
            }

            // 不能删除超级管理员
            if ("super_admin".equals(adminUser.getRole())) {
                return Result.error("不能删除超级管理员");
            }

            adminUserMapper.deleteById(id);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除后端用户失败", e);
            return Result.error("删除后端用户失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除后端用户
     */
    @DeleteMapping("/admin-users/batch")
    public Result<String> batchDeleteAdminUsers(@RequestBody Map<String, List<Long>> params) {
        try {
            List<Long> ids = params.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.paramError("用户ID列表不能为空");
            }

            // 检查是否包含超级管理员
            LambdaQueryWrapper<AdminUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(AdminUser::getId, ids);
            wrapper.eq(AdminUser::getRole, "super_admin");
            Long superAdminCount = adminUserMapper.selectCount(wrapper);
            if (superAdminCount > 0) {
                return Result.error("不能删除超级管理员");
            }

            int deletedCount = adminUserMapper.deleteBatchIds(ids);
            return Result.success("成功删除 " + deletedCount + " 个用户");
        } catch (Exception e) {
            log.error("批量删除后端用户失败", e);
            return Result.error("批量删除后端用户失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新后端用户状态
     */
    @PutMapping("/admin-users/batch/status")
    public Result<String> batchUpdateAdminUserStatus(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) params.get("ids");
            Integer status = (Integer) params.get("status");

            if (ids == null || ids.isEmpty()) {
                return Result.paramError("用户ID列表不能为空");
            }
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            // 批量更新状态
            for (Long id : ids) {
                AdminUser adminUser = adminUserMapper.selectById(id);
                if (adminUser != null) {
                    adminUser.setStatus(status);
                    adminUser.setUpdatedAt(LocalDateTime.now());
                    adminUserMapper.updateById(adminUser);
                }
            }

            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success("成功" + statusText + " " + ids.size() + " 个用户");
        } catch (Exception e) {
            log.error("批量更新后端用户状态失败", e);
            return Result.error("批量更新后端用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }

    /**
     * 获取订单列表
     */
    @GetMapping("/orders")
    public Result<PageResult<Map<String, Object>>> getOrders(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "created_at") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        try {
            Page<Order> page = new Page<>(current, size);
            QueryWrapper<Order> wrapper = new QueryWrapper<>();

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.like("recipient_name", keyword)
                       .or()
                       .like("recipient_phone", keyword)
                       .or()
                       .like("order_no", keyword);
            }

            // 状态筛选
            if (status != null) {
                wrapper.eq("status", status);
            }

            // 排序
            if ("asc".equalsIgnoreCase(sortOrder)) {
                wrapper.orderByAsc(sortBy);
            } else {
                wrapper.orderByDesc(sortBy);
            }

            Page<Order> result = orderMapper.selectPage(page, wrapper);

            // 获取用户信息并组装返回数据
            List<Map<String, Object>> enrichedOrders = new ArrayList<>();
            for (Order order : result.getRecords()) {
                Map<String, Object> orderMap = new HashMap<>();

                // 复制订单基本信息
                orderMap.put("id", order.getId());
                orderMap.put("orderNo", order.getOrderNo());
                orderMap.put("userId", order.getUserId());
                orderMap.put("totalAmount", order.getTotalAmount());
                orderMap.put("discountAmount", order.getDiscountAmount());
                orderMap.put("finalAmount", order.getFinalAmount());
                orderMap.put("status", order.getStatus());
                orderMap.put("paymentMethod", order.getPaymentMethod());
                orderMap.put("paymentStatus", order.getPaymentStatus());
                orderMap.put("recipientName", order.getRecipientName());
                orderMap.put("recipientPhone", order.getRecipientPhone());
                orderMap.put("recipientAddress", order.getRecipientAddress());
                orderMap.put("deliveryNotes", order.getDeliveryNotes());
                orderMap.put("remark", order.getRemark());
                orderMap.put("deliveryType", order.getDeliveryType());
                orderMap.put("pickupName", order.getPickupName());
                orderMap.put("pickupPhone", order.getPickupPhone());
                orderMap.put("pickupTime", order.getPickupTime());
                orderMap.put("deliveryTime", order.getDeliveryTime());
                orderMap.put("paidAt", order.getPaidAt());
                orderMap.put("shippedAt", order.getShippedAt());
                orderMap.put("deliveredAt", order.getDeliveredAt());
                orderMap.put("createdAt", order.getCreatedAt());
                orderMap.put("updatedAt", order.getUpdatedAt());

                // 获取用户信息
                User user = userMapper.selectById(order.getUserId());
                if (user != null) {
                    orderMap.put("userName", user.getNickname());
                    orderMap.put("userPhone", user.getPhone());
                    orderMap.put("userAvatar", user.getAvatarUrl());
                } else {
                    orderMap.put("userName", "未知用户");
                    orderMap.put("userPhone", "");
                    orderMap.put("userAvatar", "");
                }

                enrichedOrders.add(orderMap);
            }

            return Result.success(new PageResult<>(
                enrichedOrders,
                result.getTotal(),
                result.getCurrent(),
                result.getSize()
            ));
        } catch (Exception e) {
            return Result.error("获取订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/orders/{id}")
    public Result<Map<String, Object>> getOrderDetail(@PathVariable Long id) {
        try {
            Order order = orderMapper.selectById(id);
            if (order == null) {
                return Result.notFound("订单不存在");
            }

            Map<String, Object> orderDetail = new HashMap<>();

            // 复制订单基本信息
            orderDetail.put("id", order.getId());
            orderDetail.put("orderNo", order.getOrderNo());
            orderDetail.put("userId", order.getUserId());
            orderDetail.put("totalAmount", order.getTotalAmount());
            orderDetail.put("discountAmount", order.getDiscountAmount());
            orderDetail.put("finalAmount", order.getFinalAmount());
            orderDetail.put("status", order.getStatus());
            orderDetail.put("paymentMethod", order.getPaymentMethod());
            orderDetail.put("paymentStatus", order.getPaymentStatus());
            orderDetail.put("recipientName", order.getRecipientName());
            orderDetail.put("recipientPhone", order.getRecipientPhone());
            orderDetail.put("recipientAddress", order.getRecipientAddress());
            orderDetail.put("deliveryNotes", order.getDeliveryNotes());
            orderDetail.put("remark", order.getRemark());
            orderDetail.put("deliveryType", order.getDeliveryType());
            orderDetail.put("pickupName", order.getPickupName());
            orderDetail.put("pickupPhone", order.getPickupPhone());
            orderDetail.put("pickupTime", order.getPickupTime());
            orderDetail.put("deliveryTime", order.getDeliveryTime());
            orderDetail.put("paidAt", order.getPaidAt());
            orderDetail.put("shippedAt", order.getShippedAt());
            orderDetail.put("deliveredAt", order.getDeliveredAt());
            orderDetail.put("createdAt", order.getCreatedAt());
            orderDetail.put("updatedAt", order.getUpdatedAt());

            // 获取用户信息
            User user = userMapper.selectById(order.getUserId());
            if (user != null) {
                orderDetail.put("userName", user.getNickname());
                orderDetail.put("userPhone", user.getPhone());
                orderDetail.put("userAvatar", user.getAvatarUrl());
            } else {
                orderDetail.put("userName", "未知用户");
                orderDetail.put("userPhone", "");
                orderDetail.put("userAvatar", "");
            }

            // 获取订单商品列表
            LambdaQueryWrapper<OrderItem> itemWrapper = new LambdaQueryWrapper<>();
            itemWrapper.eq(OrderItem::getOrderId, id);
            List<OrderItem> orderItems = orderItemMapper.selectList(itemWrapper);
            orderDetail.put("items", orderItems);

            return Result.success(orderDetail);
        } catch (Exception e) {
            log.error("获取订单详情失败", e);
            return Result.error("获取订单详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/orders/{id}/status")
    public Result<String> updateOrderStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            Order order = orderMapper.selectById(id);
            if (order == null) {
                return Result.notFound("订单不存在");
            }

            // 验证状态转换的合法性
            if (!isValidStatusTransition(order.getStatus(), order.getPaymentStatus(), status)) {
                return Result.error("无效的状态转换");
            }

            // 更新订单状态
            orderService.updateOrderStatus(id, status);

            String statusText = getOrderStatusText(status);
            return Result.success("订单状态已更新为" + statusText);
        } catch (Exception e) {
            log.error("更新订单状态失败", e);
            return Result.error("更新订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 确认付款（管理员确认客户已付款）
     */
    @PutMapping("/orders/{id}/confirm-payment")
    public Result<String> confirmPayment(@PathVariable Long id) {
        try {
            Order order = orderMapper.selectById(id);
            if (order == null) {
                return Result.notFound("订单不存在");
            }

            if (order.getStatus() != 1) {
                return Result.error("只有待付款状态的订单才能确认付款");
            }

            if (order.getPaymentStatus() == 1) {
                return Result.error("订单已确认付款");
            }

            // 只更新支付状态，不改变订单状态，让用户可以确认收货
            orderService.confirmPayment(id);

            return Result.success("付款确认成功，等待用户确认收货");
        } catch (Exception e) {
            log.error("确认付款失败", e);
            return Result.error("确认付款失败: " + e.getMessage());
        }
    }

    /**
     * 验证订单状态转换是否合法
     */
    private boolean isValidStatusTransition(Integer currentStatus, Integer paymentStatus, Integer newStatus) {
        // 已取消的订单不能再更改状态
        if (currentStatus == 5) {
            return false;
        }

        // 已完成的订单只能取消（特殊情况）
        if (currentStatus == 4 && newStatus != 5) {
            return false;
        }

        // 管理员订单状态流程（基于货到付款模式）：
        // 2(已下单) -> 3(配送中), 5(已取消)
        // 3(配送中) -> 1(待付款), 5(已取消)
        // 1(待付款) -> 5(已取消) (管理员不能直接改为已完成，需要用户确认收货)
        // 4(已完成) -> 5(已取消)

        switch (currentStatus) {
            case 2: // 已下单
                return newStatus == 3 || newStatus == 5;
            case 3: // 配送中
                return newStatus == 1 || newStatus == 5;
            case 1: // 待付款
                // 管理员不能直接将待付款改为已完成，需要通过confirmPayment API确认付款，然后用户确认收货
                return newStatus == 5;
            case 4: // 已完成
                return newStatus == 5; // 只能取消
            default:
                return false;
        }
    }

    /**
     * 获取订单状态文本
     */
    private String getOrderStatusText(Integer status) {
        switch (status) {
            case 1: return "待付款";
            case 2: return "已下单";
            case 3: return "配送中";
            case 4: return "已完成";
            case 5: return "已取消";
            default: return "未知状态";
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/users/{id}/status")
    public Result<String> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.notFound("用户不存在");
            }

            user.setStatus(status);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success("用户状态已更新为" + statusText);
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return Result.error("更新用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{id}")
    public Result<String> deleteUser(@PathVariable Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.notFound("用户不存在");
            }

            userMapper.deleteById(id);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/users/batch")
    public Result<String> batchDeleteUsers(@RequestBody Map<String, List<Long>> params) {
        try {
            List<Long> ids = params.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.paramError("用户ID列表不能为空");
            }

            int deletedCount = userMapper.deleteBatchIds(ids);
            return Result.success("成功删除 " + deletedCount + " 个用户");
        } catch (Exception e) {
            log.error("批量删除用户失败", e);
            return Result.error("批量删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类列表
     */
    @GetMapping("/categories")
    public Result<PageResult<Category>> getCategories(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status) {
        try {
            Page<Category> page = new Page<>(current, size);
            LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.like(Category::getName, keyword)
                       .or()
                       .like(Category::getDescription, keyword);
            }

            // 状态筛选
            if (status != null) {
                wrapper.eq(Category::getStatus, status);
            }

            // 按排序顺序排序
            wrapper.orderByAsc(Category::getSortOrder);

            Page<Category> result = categoryMapper.selectPage(page, wrapper);

            return Result.success(new PageResult<>(
                result.getRecords(),
                result.getTotal(),
                result.getCurrent(),
                result.getSize()
            ));
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            return Result.error("获取分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建分类
     */
    @PostMapping("/categories")
    public Result<Category> createCategory(@RequestBody Category category) {
        try {
            // 验证必填字段
            if (category.getName() == null || category.getName().trim().isEmpty()) {
                return Result.paramError("分类名称不能为空");
            }

            // 检查名称是否重复
            LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Category::getName, category.getName().trim());
            Category existing = categoryMapper.selectOne(wrapper);
            if (existing != null) {
                return Result.paramError("分类名称已存在");
            }

            // 设置默认值
            category.setName(category.getName().trim());
            if (category.getStatus() == null) {
                category.setStatus(1);
            }
            if (category.getSortOrder() == null) {
                category.setSortOrder(0);
            }
            category.setCreatedAt(LocalDateTime.now());
            category.setUpdatedAt(LocalDateTime.now());

            categoryMapper.insert(category);
            return Result.success("分类创建成功", category);
        } catch (Exception e) {
            log.error("创建分类失败", e);
            return Result.error("创建分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新分类
     */
    @PutMapping("/categories/{id}")
    public Result<Category> updateCategory(@PathVariable Long id, @RequestBody Category category) {
        try {
            Category existing = categoryMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("分类不存在");
            }

            // 验证必填字段
            if (category.getName() == null || category.getName().trim().isEmpty()) {
                return Result.paramError("分类名称不能为空");
            }

            // 检查名称是否重复（排除自己）
            LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Category::getName, category.getName().trim())
                   .ne(Category::getId, id);
            Category duplicate = categoryMapper.selectOne(wrapper);
            if (duplicate != null) {
                return Result.paramError("分类名称已存在");
            }

            // 更新字段
            existing.setName(category.getName().trim());
            if (category.getDescription() != null) {
                existing.setDescription(category.getDescription());
            }
            if (category.getImageUrl() != null) {
                existing.setImageUrl(category.getImageUrl());
            }
            if (category.getStatus() != null) {
                existing.setStatus(category.getStatus());
            }
            if (category.getSortOrder() != null) {
                existing.setSortOrder(category.getSortOrder());
            }
            existing.setUpdatedAt(LocalDateTime.now());

            categoryMapper.updateById(existing);
            return Result.success("分类更新成功", existing);
        } catch (Exception e) {
            log.error("更新分类失败", e);
            return Result.error("更新分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新分类状态
     */
    @PutMapping("/categories/{id}/status")
    public Result<String> updateCategoryStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            Category category = categoryMapper.selectById(id);
            if (category == null) {
                return Result.notFound("分类不存在");
            }

            category.setStatus(status);
            category.setUpdatedAt(LocalDateTime.now());
            categoryMapper.updateById(category);

            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success("分类已" + statusText);
        } catch (Exception e) {
            log.error("更新分类状态失败", e);
            return Result.error("更新分类状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/categories/{id}")
    public Result<String> deleteCategory(@PathVariable Long id) {
        try {
            Category category = categoryMapper.selectById(id);
            if (category == null) {
                return Result.notFound("分类不存在");
            }

            // 检查是否有商品使用此分类
            LambdaQueryWrapper<Flower> flowerWrapper = new LambdaQueryWrapper<>();
            flowerWrapper.eq(Flower::getCategoryId, id);
            Long flowerCount = flowerMapper.selectCount(flowerWrapper);
            if (flowerCount > 0) {
                return Result.error("该分类下还有 " + flowerCount + " 个商品，无法删除");
            }

            categoryMapper.deleteById(id);
            return Result.success("分类删除成功");
        } catch (Exception e) {
            log.error("删除分类失败", e);
            return Result.error("删除分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品列表
     */
    @GetMapping("/flowers")
    public Result<PageResult<FlowerVO>> getFlowers(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String categories,
            @RequestParam(required = false) String statuses,
            @RequestParam(required = false) String colors,
            @RequestParam(required = false) String sizes,
            @RequestParam(required = false) String priceRanges,
            @RequestParam(required = false) String stockRanges) {
        try {
            Page<Flower> page = new Page<>(current, size);
            LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.like(Flower::getName, keyword)
                       .or()
                       .like(Flower::getDescription, keyword);
            }

            // 分类筛选
            if (categoryId != null) {
                wrapper.eq(Flower::getCategoryId, categoryId);
            }

            // 状态筛选
            if (status != null) {
                wrapper.eq(Flower::getStatus, status);
            }

            // 多分类筛选
            if (categories != null && !categories.trim().isEmpty()) {
                String[] categoryArray = categories.split(",");
                List<Long> categoryIds = Arrays.stream(categoryArray)
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(s -> {
                        try {
                            return Long.parseLong(s);
                        } catch (NumberFormatException e) {
                            log.warn("分类ID格式错误: {}", s);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                if (!categoryIds.isEmpty()) {
                    wrapper.in(Flower::getCategoryId, categoryIds);
                }
            }

            // 多状态筛选
            if (statuses != null && !statuses.trim().isEmpty()) {
                String[] statusArray = statuses.split(",");
                List<Integer> statusList = Arrays.stream(statusArray)
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(s -> {
                        try {
                            return Integer.parseInt(s);
                        } catch (NumberFormatException e) {
                            log.warn("状态值格式错误: {}", s);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                if (!statusList.isEmpty()) {
                    wrapper.in(Flower::getStatus, statusList);
                }
            }

            // 颜色筛选
            if (colors != null && !colors.trim().isEmpty()) {
                String[] colorArray = colors.split(",");
                // 过滤掉空字符串
                List<String> validColors = Arrays.stream(colorArray)
                    .map(String::trim)
                    .filter(c -> !c.isEmpty())
                    .collect(Collectors.toList());

                if (!validColors.isEmpty()) {
                    wrapper.and(w -> {
                        for (int i = 0; i < validColors.size(); i++) {
                            if (i == 0) {
                                w.like(Flower::getColor, validColors.get(i));
                            } else {
                                w.or().like(Flower::getColor, validColors.get(i));
                            }
                        }
                    });
                }
            }

            // 规格筛选
            if (sizes != null && !sizes.trim().isEmpty()) {
                String[] sizeArray = sizes.split(",");
                // 过滤掉空字符串
                List<String> validSizes = Arrays.stream(sizeArray)
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());

                if (!validSizes.isEmpty()) {
                    wrapper.and(w -> {
                        for (int i = 0; i < validSizes.size(); i++) {
                            if (i == 0) {
                                w.like(Flower::getSize, validSizes.get(i));
                            } else {
                                w.or().like(Flower::getSize, validSizes.get(i));
                            }
                        }
                    });
                }
            }

            // 价格范围筛选 - 简化版本
            if (priceRanges != null && !priceRanges.trim().isEmpty()) {
                // 简单的价格范围筛选，支持格式如: "0-50,100-200"
                String[] ranges = priceRanges.replace("[", "").replace("]", "").split(",");
                List<String> validRanges = Arrays.stream(ranges)
                    .map(String::trim)
                    .filter(range -> !range.isEmpty() && range.contains("-"))
                    .collect(Collectors.toList());

                if (!validRanges.isEmpty()) {
                    List<BigDecimal[]> priceRangeList = new ArrayList<>();

                    for (String range : validRanges) {
                        String[] parts = range.split("-");
                        if (parts.length == 2) {
                            try {
                                BigDecimal min = new BigDecimal(parts[0].trim());
                                BigDecimal max = new BigDecimal(parts[1].trim());
                                if (min.compareTo(BigDecimal.ZERO) >= 0 && max.compareTo(min) >= 0) {
                                    priceRangeList.add(new BigDecimal[]{min, max});
                                }
                            } catch (NumberFormatException e) {
                                log.warn("价格范围格式错误: {}", range);
                            }
                        }
                    }

                    if (!priceRangeList.isEmpty()) {
                        wrapper.and(w -> {
                            for (int i = 0; i < priceRangeList.size(); i++) {
                                BigDecimal[] priceRange = priceRangeList.get(i);
                                if (i == 0) {
                                    w.between(Flower::getPrice, priceRange[0], priceRange[1]);
                                } else {
                                    w.or().between(Flower::getPrice, priceRange[0], priceRange[1]);
                                }
                            }
                        });
                    }
                }
            }

            // 库存范围筛选 - 简化版本
            if (stockRanges != null && !stockRanges.trim().isEmpty()) {
                // 简单的库存范围筛选，支持格式如: "0-10,10-100"
                String[] ranges = stockRanges.replace("[", "").replace("]", "").split(",");
                List<String> validRanges = Arrays.stream(ranges)
                    .map(String::trim)
                    .filter(range -> !range.isEmpty() && range.contains("-"))
                    .collect(Collectors.toList());

                if (!validRanges.isEmpty()) {
                    List<Integer[]> stockRangeList = new ArrayList<>();

                    for (String range : validRanges) {
                        String[] parts = range.split("-");
                        if (parts.length == 2) {
                            try {
                                Integer min = Integer.parseInt(parts[0].trim());
                                Integer max = Integer.parseInt(parts[1].trim());
                                if (min >= 0 && max >= min) {
                                    stockRangeList.add(new Integer[]{min, max});
                                }
                            } catch (NumberFormatException e) {
                                log.warn("库存范围格式错误: {}", range);
                            }
                        }
                    }

                    if (!stockRangeList.isEmpty()) {
                        wrapper.and(w -> {
                            for (int i = 0; i < stockRangeList.size(); i++) {
                                Integer[] stockRange = stockRangeList.get(i);
                                if (i == 0) {
                                    w.between(Flower::getStockQuantity, stockRange[0], stockRange[1]);
                                } else {
                                    w.or().between(Flower::getStockQuantity, stockRange[0], stockRange[1]);
                                }
                            }
                        });
                    }
                }
            }

            // 按创建时间倒序
            wrapper.orderByDesc(Flower::getCreatedAt);

            Page<Flower> result = flowerMapper.selectPage(page, wrapper);

            // 转换为FlowerVO并填充分类名称
            List<FlowerVO> flowerVOs = result.getRecords().stream()
                    .map(flower -> {
                        FlowerVO vo = FlowerVO.fromFlower(flower);
                        // 获取分类名称
                        if (flower.getCategoryId() != null) {
                            Category category = categoryMapper.selectById(flower.getCategoryId());
                            if (category != null) {
                                vo.setCategoryName(category.getName());
                            }
                        }
                        return vo;
                    })
                    .collect(Collectors.toList());

            return Result.success(new PageResult<>(
                flowerVOs,
                result.getTotal(),
                result.getCurrent(),
                result.getSize()
            ));
        } catch (Exception e) {
            log.error("获取商品列表失败", e);
            return Result.error("获取商品列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品详情
     */
    @GetMapping("/flowers/{id}")
    public Result<FlowerVO> getFlowerDetail(@PathVariable Long id) {
        try {
            Flower flower = flowerMapper.selectById(id);
            if (flower == null) {
                return Result.notFound("商品不存在");
            }

            // 转换为FlowerVO并填充分类名称
            FlowerVO vo = FlowerVO.fromFlower(flower);
            if (flower.getCategoryId() != null) {
                Category category = categoryMapper.selectById(flower.getCategoryId());
                if (category != null) {
                    vo.setCategoryName(category.getName());
                }
            }

            return Result.success(vo);
        } catch (Exception e) {
            log.error("获取商品详情失败", e);
            return Result.error("获取商品详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建商品
     */
    @PostMapping("/flowers")
    public Result<Flower> createFlower(@RequestBody Map<String, Object> flowerData) {
        try {
            // 验证必填字段
            String name = (String) flowerData.get("name");
            if (name == null || name.trim().isEmpty()) {
                return Result.paramError("商品名称不能为空");
            }

            Object categoryIdObj = flowerData.get("categoryId");
            if (categoryIdObj == null) {
                return Result.paramError("商品分类不能为空");
            }
            Long categoryId = Long.valueOf(categoryIdObj.toString());

            Object priceObj = flowerData.get("price");
            if (priceObj == null) {
                return Result.paramError("商品价格不能为空");
            }
            BigDecimal price = new BigDecimal(priceObj.toString());
            if (price.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.paramError("商品价格必须大于0");
            }

            // 验证分类是否存在
            Category category = categoryMapper.selectById(categoryId);
            if (category == null) {
                return Result.paramError("商品分类不存在");
            }

            // 创建商品对象
            Flower flower = new Flower();
            flower.setName(name.trim());
            flower.setCategoryId(categoryId);
            flower.setPrice(price);

            // 设置可选字段
            if (flowerData.containsKey("description")) {
                flower.setDescription((String) flowerData.get("description"));
            }
            if (flowerData.containsKey("originalPrice")) {
                Object originalPriceObj = flowerData.get("originalPrice");
                if (originalPriceObj != null && !originalPriceObj.toString().isEmpty()) {
                    flower.setOriginalPrice(new BigDecimal(originalPriceObj.toString()));
                }
            }
            if (flowerData.containsKey("stockQuantity")) {
                flower.setStockQuantity(Integer.valueOf(flowerData.get("stockQuantity").toString()));
            } else {
                flower.setStockQuantity(0);
            }
            if (flowerData.containsKey("status")) {
                flower.setStatus(Integer.valueOf(flowerData.get("status").toString()));
            } else {
                flower.setStatus(1);
            }
            if (flowerData.containsKey("mainImage")) {
                flower.setMainImage((String) flowerData.get("mainImage"));
            }
            // 处理详情图片 - 前端传的是detailImages，后端存储为images
            if (flowerData.containsKey("detailImages")) {
                flower.setImages((String) flowerData.get("detailImages"));
            }

            // 新增字段
            if (flowerData.containsKey("tags")) {
                flower.setTags((String) flowerData.get("tags"));
            }
            if (flowerData.containsKey("flowerLanguage")) {
                flower.setFlowerLanguage((String) flowerData.get("flowerLanguage"));
            }
            if (flowerData.containsKey("careInstructions")) {
                flower.setCareInstructions((String) flowerData.get("careInstructions"));
            }
            if (flowerData.containsKey("occasion")) {
                flower.setOccasion((String) flowerData.get("occasion"));
            }
            if (flowerData.containsKey("color")) {
                flower.setColor((String) flowerData.get("color"));
            }
            if (flowerData.containsKey("size")) {
                flower.setSize((String) flowerData.get("size"));
            }
            if (flowerData.containsKey("isFeatured")) {
                flower.setIsFeatured(Integer.valueOf(flowerData.get("isFeatured").toString()));
            } else {
                flower.setIsFeatured(0);
            }

            // 设置默认值
            flower.setSalesCount(0);
            flower.setCreatedAt(LocalDateTime.now());
            flower.setUpdatedAt(LocalDateTime.now());

            flowerMapper.insert(flower);
            return Result.success("商品创建成功", flower);
        } catch (Exception e) {
            log.error("创建商品失败", e);
            return Result.error("创建商品失败: " + e.getMessage());
        }
    }

    /**
     * 更新商品
     */
    @PutMapping("/flowers/{id}")
    public Result<Flower> updateFlower(@PathVariable Long id, @RequestBody Map<String, Object> flowerData) {
        try {
            log.info("更新商品请求，ID: {}, 数据: {}", id, flowerData);
            log.info("接收到的字段: {}", flowerData.keySet());

            Flower existing = flowerMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("商品不存在");
            }

            log.info("更新前的商品数据: {}", existing);

            // 验证必填字段
            String name = (String) flowerData.get("name");
            if (name != null && name.trim().isEmpty()) {
                return Result.paramError("商品名称不能为空");
            }

            Object priceObj = flowerData.get("price");
            if (priceObj != null) {
                BigDecimal price = new BigDecimal(priceObj.toString());
                if (price.compareTo(BigDecimal.ZERO) <= 0) {
                    return Result.paramError("商品价格必须大于0");
                }
            }

            // 验证分类是否存在
            Object categoryIdObj = flowerData.get("categoryId");
            if (categoryIdObj != null) {
                Long categoryId = Long.valueOf(categoryIdObj.toString());
                Category category = categoryMapper.selectById(categoryId);
                if (category == null) {
                    return Result.paramError("商品分类不存在");
                }
                existing.setCategoryId(categoryId);
            }

            // 更新字段
            if (flowerData.containsKey("name")) {
                existing.setName(((String) flowerData.get("name")).trim());
            }
            if (flowerData.containsKey("description")) {
                existing.setDescription((String) flowerData.get("description"));
            }
            if (flowerData.containsKey("price")) {
                existing.setPrice(new BigDecimal(flowerData.get("price").toString()));
            }
            if (flowerData.containsKey("originalPrice")) {
                Object originalPriceObj = flowerData.get("originalPrice");
                if (originalPriceObj != null) {
                    existing.setOriginalPrice(new BigDecimal(originalPriceObj.toString()));
                }
            }
            if (flowerData.containsKey("stockQuantity")) {
                existing.setStockQuantity(Integer.valueOf(flowerData.get("stockQuantity").toString()));
            }
            if (flowerData.containsKey("status")) {
                existing.setStatus(Integer.valueOf(flowerData.get("status").toString()));
            }
            if (flowerData.containsKey("mainImage")) {
                existing.setMainImage((String) flowerData.get("mainImage"));
            }
            // 处理详情图片 - 前端传的是detailImages，后端存储为images
            if (flowerData.containsKey("detailImages")) {
                existing.setImages((String) flowerData.get("detailImages"));
            }

            // 更新新增字段
            if (flowerData.containsKey("tags")) {
                existing.setTags((String) flowerData.get("tags"));
            }
            if (flowerData.containsKey("flowerLanguage")) {
                existing.setFlowerLanguage((String) flowerData.get("flowerLanguage"));
            }
            if (flowerData.containsKey("careInstructions")) {
                existing.setCareInstructions((String) flowerData.get("careInstructions"));
            }
            if (flowerData.containsKey("occasion")) {
                existing.setOccasion((String) flowerData.get("occasion"));
            }
            if (flowerData.containsKey("color")) {
                existing.setColor((String) flowerData.get("color"));
            }
            if (flowerData.containsKey("size")) {
                existing.setSize((String) flowerData.get("size"));
            }
            if (flowerData.containsKey("isFeatured")) {
                existing.setIsFeatured(Integer.valueOf(flowerData.get("isFeatured").toString()));
            }

            existing.setUpdatedAt(LocalDateTime.now());

            log.info("更新后的商品数据: {}", existing);
            flowerMapper.updateById(existing);
            log.info("数据库更新完成");
            return Result.success("商品更新成功", existing);
        } catch (Exception e) {
            log.error("更新商品失败", e);
            return Result.error("更新商品失败: " + e.getMessage());
        }
    }

    /**
     * 更新商品状态
     */
    @PutMapping("/flowers/{id}/status")
    public Result<String> updateFlowerStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            Flower flower = flowerMapper.selectById(id);
            if (flower == null) {
                return Result.notFound("商品不存在");
            }

            flower.setStatus(status);
            flower.setUpdatedAt(LocalDateTime.now());
            flowerMapper.updateById(flower);

            String statusText = status == 1 ? "上架" : "下架";
            return Result.success("商品已" + statusText);
        } catch (Exception e) {
            log.error("更新商品状态失败", e);
            return Result.error("更新商品状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/flowers/{id}")
    public Result<String> deleteFlower(@PathVariable Long id) {
        try {
            Flower flower = flowerMapper.selectById(id);
            if (flower == null) {
                return Result.notFound("商品不存在");
            }

            flowerMapper.deleteById(id);
            return Result.success("商品删除成功");
        } catch (Exception e) {
            log.error("删除商品失败", e);
            return Result.error("删除商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取评价列表
     */
    @GetMapping("/reviews")
    public Result<PageResult<Map<String, Object>>> getReviews(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer rating) {
        try {
            Page<FlowerReview> page = new Page<>(current, size);
            LambdaQueryWrapper<FlowerReview> wrapper = new LambdaQueryWrapper<>();

            // 状态筛选
            if (status != null) {
                wrapper.eq(FlowerReview::getStatus, status);
            }

            // 评分筛选
            if (rating != null) {
                wrapper.eq(FlowerReview::getRating, rating);
            }

            // 按创建时间倒序
            wrapper.orderByDesc(FlowerReview::getCreatedAt);

            Page<FlowerReview> reviewPage = flowerReviewMapper.selectPage(page, wrapper);

            // 组装返回数据，包含用户和商品信息
            List<Map<String, Object>> reviewList = new ArrayList<>();
            for (FlowerReview review : reviewPage.getRecords()) {
                Map<String, Object> reviewMap = new HashMap<>();
                reviewMap.put("id", review.getId());
                reviewMap.put("userId", review.getUserId());
                reviewMap.put("flowerId", review.getFlowerId());
                reviewMap.put("orderId", review.getOrderId());
                reviewMap.put("rating", review.getRating());
                reviewMap.put("content", review.getContent());
                reviewMap.put("images", review.getImages());
                reviewMap.put("status", review.getStatus());
                reviewMap.put("createdAt", review.getCreatedAt());

                // 获取用户信息
                User user = userMapper.selectById(review.getUserId());
                if (user != null) {
                    reviewMap.put("userName", user.getNickname() != null ? user.getNickname() : "匿名用户");
                    reviewMap.put("userAvatar", user.getAvatarUrl());
                } else {
                    reviewMap.put("userName", "匿名用户");
                    reviewMap.put("userAvatar", "");
                }

                // 获取商品信息
                Flower flower = flowerMapper.selectById(review.getFlowerId());
                if (flower != null) {
                    reviewMap.put("flowerName", flower.getName());
                } else {
                    reviewMap.put("flowerName", "商品已删除");
                }

                // 关键词搜索过滤
                if (keyword != null && !keyword.trim().isEmpty()) {
                    String searchKeyword = keyword.toLowerCase();
                    String userName = (String) reviewMap.get("userName");
                    String flowerName = (String) reviewMap.get("flowerName");
                    String content = review.getContent();

                    if ((userName != null && userName.toLowerCase().contains(searchKeyword)) ||
                        (flowerName != null && flowerName.toLowerCase().contains(searchKeyword)) ||
                        (content != null && content.toLowerCase().contains(searchKeyword))) {
                        reviewList.add(reviewMap);
                    }
                } else {
                    reviewList.add(reviewMap);
                }
            }

            return Result.success(new PageResult<>(
                reviewList,
                reviewPage.getTotal(),
                reviewPage.getCurrent(),
                reviewPage.getSize()
            ));
        } catch (Exception e) {
            log.error("获取评价列表失败", e);
            return Result.error("获取评价列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新评价状态
     */
    @PutMapping("/reviews/{id}/status")
    public Result<String> updateReviewStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            FlowerReview review = flowerReviewMapper.selectById(id);
            if (review == null) {
                return Result.notFound("评价不存在");
            }

            review.setStatus(status);
            review.setUpdatedAt(LocalDateTime.now());
            flowerReviewMapper.updateById(review);

            String statusText = status == 1 ? "通过" : "拒绝";
            return Result.success("评价审核" + statusText);
        } catch (Exception e) {
            log.error("更新评价状态失败", e);
            return Result.error("更新评价状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除评价
     */
    @DeleteMapping("/reviews/{id}")
    public Result<String> deleteReview(@PathVariable Long id) {
        try {
            FlowerReview review = flowerReviewMapper.selectById(id);
            if (review == null) {
                return Result.notFound("评价不存在");
            }

            flowerReviewMapper.deleteById(id);
            return Result.success("评价删除成功");
        } catch (Exception e) {
            log.error("删除评价失败", e);
            return Result.error("删除评价失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除评价
     */
    @DeleteMapping("/reviews/batch")
    public Result<String> batchDeleteReviews(@RequestBody Map<String, List<Long>> params) {
        try {
            List<Long> ids = params.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.paramError("评价ID列表不能为空");
            }

            int deletedCount = flowerReviewMapper.deleteBatchIds(ids);
            return Result.success("成功删除 " + deletedCount + " 条评价");
        } catch (Exception e) {
            log.error("批量删除评价失败", e);
            return Result.error("批量删除评价失败: " + e.getMessage());
        }
    }

    /**
     * 获取省份列表
     */
    @GetMapping("/provinces")
    public Result<List<Province>> getProvinces() {
        try {
            List<Province> provinces = regionMapper.getAllProvinces();
            return Result.success(provinces);
        } catch (Exception e) {
            log.error("获取省份列表失败", e);
            return Result.error("获取省份列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取城市列表
     */
    @GetMapping("/cities/{provinceCode}")
    public Result<List<City>> getCities(@PathVariable String provinceCode) {
        try {
            List<City> cities = regionMapper.getCitiesByProvinceCode(provinceCode);
            return Result.success(cities);
        } catch (Exception e) {
            log.error("获取城市列表失败", e);
            return Result.error("获取城市列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取区县列表
     */
    @GetMapping("/districts/{cityCode}")
    public Result<List<District>> getDistricts(@PathVariable String cityCode) {
        try {
            List<District> districts = regionMapper.getDistrictsByCityCode(cityCode);
            return Result.success(districts);
        } catch (Exception e) {
            log.error("获取区县列表失败", e);
            return Result.error("获取区县列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取地址统计信息
     */
    @GetMapping("/address-stats")
    public Result<Map<String, Object>> getAddressStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取省份总数
            List<Province> provinces = regionMapper.getAllProvinces();
            stats.put("provinceCount", provinces.size());

            // 获取城市总数
            int cityCount = 0;
            for (Province province : provinces) {
                List<City> cities = regionMapper.getCitiesByProvinceCode(province.getCode());
                cityCount += cities.size();
            }
            stats.put("cityCount", cityCount);

            // 获取区县总数
            int districtCount = 0;
            for (Province province : provinces) {
                List<City> cities = regionMapper.getCitiesByProvinceCode(province.getCode());
                for (City city : cities) {
                    List<District> districts = regionMapper.getDistrictsByCityCode(city.getCode());
                    districtCount += districts.size();
                }
            }
            stats.put("districtCount", districtCount);

            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取地址统计信息失败", e);
            return Result.error("获取地址统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 添加省份
     */
    @PostMapping("/provinces")
    public Result<String> addProvince(@RequestBody Province province) {
        try {
            // 检查省份代码是否已存在
            List<Province> existingProvinces = regionMapper.getAllProvinces();
            for (Province p : existingProvinces) {
                if (p.getCode().equals(province.getCode())) {
                    return Result.error("省份代码已存在");
                }
            }

            regionMapper.insertProvince(province);
            return Result.success("省份添加成功");
        } catch (Exception e) {
            log.error("添加省份失败", e);
            return Result.error("添加省份失败: " + e.getMessage());
        }
    }

    /**
     * 更新省份
     */
    @PutMapping("/provinces/{id}")
    public Result<String> updateProvince(@PathVariable Integer id, @RequestBody Province province) {
        try {
            province.setId(id);
            regionMapper.updateProvince(province);
            return Result.success("省份更新成功");
        } catch (Exception e) {
            log.error("更新省份失败", e);
            return Result.error("更新省份失败: " + e.getMessage());
        }
    }

    /**
     * 删除省份
     */
    @DeleteMapping("/provinces/{id}")
    public Result<String> deleteProvince(@PathVariable Integer id) {
        try {
            regionMapper.deleteProvince(id);
            return Result.success("省份删除成功");
        } catch (Exception e) {
            log.error("删除省份失败", e);
            return Result.error("删除省份失败: " + e.getMessage());
        }
    }

    /**
     * 添加城市
     */
    @PostMapping("/cities")
    public Result<String> addCity(@RequestBody City city) {
        try {
            // 检查城市代码是否已存在
            List<City> existingCities = regionMapper.getCitiesByProvinceCode(city.getProvinceCode());
            for (City c : existingCities) {
                if (c.getCode().equals(city.getCode())) {
                    return Result.error("城市代码已存在");
                }
            }

            regionMapper.insertCity(city);
            return Result.success("城市添加成功");
        } catch (Exception e) {
            log.error("添加城市失败", e);
            return Result.error("添加城市失败: " + e.getMessage());
        }
    }

    /**
     * 更新城市
     */
    @PutMapping("/cities/{id}")
    public Result<String> updateCity(@PathVariable Integer id, @RequestBody City city) {
        try {
            city.setId(id);
            regionMapper.updateCity(city);
            return Result.success("城市更新成功");
        } catch (Exception e) {
            log.error("更新城市失败", e);
            return Result.error("更新城市失败: " + e.getMessage());
        }
    }

    /**
     * 删除城市
     */
    @DeleteMapping("/cities/{id}")
    public Result<String> deleteCity(@PathVariable Integer id) {
        try {
            regionMapper.deleteCity(id);
            return Result.success("城市删除成功");
        } catch (Exception e) {
            log.error("删除城市失败", e);
            return Result.error("删除城市失败: " + e.getMessage());
        }
    }

    /**
     * 添加区县
     */
    @PostMapping("/districts")
    public Result<String> addDistrict(@RequestBody District district) {
        try {
            // 检查区县代码是否已存在
            List<District> existingDistricts = regionMapper.getDistrictsByCityCode(district.getCityCode());
            for (District d : existingDistricts) {
                if (d.getCode().equals(district.getCode())) {
                    return Result.error("区县代码已存在");
                }
            }

            regionMapper.insertDistrict(district);
            return Result.success("区县添加成功");
        } catch (Exception e) {
            log.error("添加区县失败", e);
            return Result.error("添加区县失败: " + e.getMessage());
        }
    }

    /**
     * 更新区县
     */
    @PutMapping("/districts/{id}")
    public Result<String> updateDistrict(@PathVariable Integer id, @RequestBody District district) {
        try {
            district.setId(id);
            regionMapper.updateDistrict(district);
            return Result.success("区县更新成功");
        } catch (Exception e) {
            log.error("更新区县失败", e);
            return Result.error("更新区县失败: " + e.getMessage());
        }
    }

    /**
     * 删除区县
     */
    @DeleteMapping("/districts/{id}")
    public Result<String> deleteDistrict(@PathVariable Integer id) {
        try {
            regionMapper.deleteDistrict(id);
            return Result.success("区县删除成功");
        } catch (Exception e) {
            log.error("删除区县失败", e);
            return Result.error("删除区县失败: " + e.getMessage());
        }
    }

    // ==================== 价格分类管理 ====================

    /**
     * 获取价格分类列表
     */
    @GetMapping("/price-categories")
    public Result<PageResult<PriceCategory>> getPriceCategories(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status) {
        try {
            PageResult<PriceCategory> result = priceCategoryService.getPriceCategoriesByPage(current, size, keyword, status);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取价格分类列表失败", e);
            return Result.error("获取价格分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取价格分类
     */
    @GetMapping("/price-categories/{id}")
    public Result<PriceCategory> getPriceCategoryById(@PathVariable Long id) {
        try {
            PriceCategory priceCategory = priceCategoryService.getPriceCategoryById(id);
            if (priceCategory != null) {
                return Result.success(priceCategory);
            } else {
                return Result.error("价格分类不存在");
            }
        } catch (Exception e) {
            log.error("获取价格分类失败", e);
            return Result.error("获取价格分类失败: " + e.getMessage());
        }
    }

    /**
     * 创建价格分类
     */
    @PostMapping("/price-categories")
    public Result<String> createPriceCategory(@RequestBody PriceCategory priceCategory) {
        try {
            boolean success = priceCategoryService.createPriceCategory(priceCategory);
            if (success) {
                return Result.success("价格分类创建成功");
            } else {
                return Result.error("价格分类创建失败");
            }
        } catch (Exception e) {
            log.error("创建价格分类失败", e);
            return Result.error("创建价格分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新价格分类
     */
    @PutMapping("/price-categories/{id}")
    public Result<String> updatePriceCategory(@PathVariable Long id, @RequestBody PriceCategory priceCategory) {
        try {
            priceCategory.setId(id);
            boolean success = priceCategoryService.updatePriceCategory(priceCategory);
            if (success) {
                return Result.success("价格分类更新成功");
            } else {
                return Result.error("价格分类更新失败");
            }
        } catch (Exception e) {
            log.error("更新价格分类失败", e);
            return Result.error("更新价格分类失败: " + e.getMessage());
        }
    }

    /**
     * 删除价格分类
     */
    @DeleteMapping("/price-categories/{id}")
    public Result<String> deletePriceCategory(@PathVariable Long id) {
        try {
            boolean success = priceCategoryService.deletePriceCategory(id);
            if (success) {
                return Result.success("价格分类删除成功");
            } else {
                return Result.error("价格分类删除失败");
            }
        } catch (Exception e) {
            log.error("删除价格分类失败", e);
            return Result.error("删除价格分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新价格分类状态
     */
    @PutMapping("/price-categories/{id}/status")
    public Result<String> updatePriceCategoryStatus(@PathVariable Long id, @RequestBody Map<String, Integer> requestBody) {
        try {
            Integer status = requestBody.get("status");
            if (status == null) {
                return Result.error("状态参数不能为空");
            }
            boolean success = priceCategoryService.updatePriceCategoryStatus(id, status);
            if (success) {
                return Result.success("价格分类状态更新成功");
            } else {
                return Result.error("价格分类状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新价格分类状态失败", e);
            return Result.error("更新价格分类状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有启用的价格分类
     */
    @GetMapping("/price-categories/active")
    public Result<List<PriceCategory>> getActivePriceCategories() {
        try {
            List<PriceCategory> priceCategories = priceCategoryService.getAllActivePriceCategories();
            return Result.success(priceCategories);
        } catch (Exception e) {
            log.error("获取启用的价格分类失败", e);
            return Result.error("获取启用的价格分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前管理员信息
     */
    @GetMapping("/profile")
    public Result<AdminUser> getProfile(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("未提供认证token");
            }

            AdminUser admin = adminService.verifyToken(token);

            // 不返回密码
            admin.setPassword(null);
            return Result.success(admin);
        } catch (Exception e) {
            log.error("获取管理员信息失败", e);
            return Result.error("获取管理员信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新管理员基本信息
     */
    @PutMapping("/profile")
    public Result<String> updateProfile(@RequestBody AdminUser adminUser, HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("未提供认证token");
            }

            AdminUser currentAdmin = adminService.verifyToken(token);
            adminUser.setId(currentAdmin.getId());
            adminUser.setUpdatedAt(LocalDateTime.now());

            // 不允许修改用户名和密码
            adminUser.setUsername(null);
            adminUser.setPassword(null);

            int result = adminUserMapper.updateById(adminUser);
            if (result > 0) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新管理员信息失败", e);
            return Result.error("更新管理员信息失败: " + e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public Result<String> updatePassword(@RequestBody Map<String, String> passwordData, HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("未提供认证token");
            }

            AdminUser admin = adminService.verifyToken(token);

            if (admin == null) {
                return Result.error("管理员不存在");
            }

            String currentPassword = passwordData.get("currentPassword");
            String newPassword = passwordData.get("newPassword");

            if (currentPassword == null || currentPassword.trim().isEmpty()) {
                return Result.error("请输入当前密码");
            }

            if (newPassword == null || newPassword.trim().isEmpty()) {
                return Result.error("请输入新密码");
            }

            if (newPassword.length() < 6) {
                return Result.error("新密码长度不能少于6位");
            }

            // 验证当前密码
            boolean isCurrentPasswordValid = false;
            if (admin.getPassword().startsWith("$2a$") || admin.getPassword().startsWith("$2b$")) {
                // 已加密的密码，使用BCrypt验证
                isCurrentPasswordValid = PasswordUtil.matches(currentPassword, admin.getPassword());
            } else {
                // 明文密码，直接比较（兼容旧数据）
                isCurrentPasswordValid = admin.getPassword().equals(currentPassword);
            }

            if (!isCurrentPasswordValid) {
                return Result.error("当前密码错误");
            }

            // 加密新密码
            String encodedNewPassword = PasswordUtil.encode(newPassword);

            // 更新密码
            admin.setPassword(encodedNewPassword);
            admin.setUpdatedAt(LocalDateTime.now());

            int result = adminUserMapper.updateById(admin);
            if (result > 0) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("密码修改失败");
            }
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error("修改密码失败: " + e.getMessage());
        }
    }

    /**
     * 上传管理员头像
     */
    @PostMapping("/upload/avatar")
    public Result<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("未提供认证token");
            }

            AdminUser admin = adminService.verifyToken(token);
            Long adminId = admin.getId();

            // 使用文件上传服务
            Map<String, String> uploadResult = fileUploadService.uploadAdminAvatar(file, adminId);
            String avatarUrl = uploadResult.get("url");

            // 更新管理员头像
            AdminUser updateAdmin = new AdminUser();
            updateAdmin.setId(adminId);
            updateAdmin.setAvatar(avatarUrl);
            updateAdmin.setUpdatedAt(LocalDateTime.now());
            adminUserMapper.updateById(updateAdmin);

            return Result.success(uploadResult);
        } catch (Exception e) {
            log.error("上传头像失败", e);
            return Result.error("上传头像失败: " + e.getMessage());
        }
    }

    /**
     * 测试头像访问
     */
    @GetMapping("/test/avatar/{filename}")
    public Result<Map<String, Object>> testAvatarAccess(@PathVariable String filename) {
        try {
            String projectPath = System.getProperty("user.dir");
            String filePath = projectPath + "/src/main/resources/image/admin-image/" + filename;
            File file = new File(filePath);

            Map<String, Object> result = new HashMap<>();
            result.put("filename", filename);
            result.put("filePath", filePath);
            result.put("fileExists", file.exists());
            result.put("fileSize", file.exists() ? file.length() : 0);
            result.put("accessUrl", "http://localhost:8080/api/image/admin-image/" + filename);
            result.put("projectPath", projectPath);

            return Result.success(result);
        } catch (Exception e) {
            log.error("测试头像访问失败", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }

}
