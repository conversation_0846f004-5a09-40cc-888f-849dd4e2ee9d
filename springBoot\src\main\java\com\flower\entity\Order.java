package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("orders")
public class Order {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 实付金额
     */
    private BigDecimal finalAmount;

    /**
     * 订单状态：1待付款，2已付款，3已发货，4已完成，5已取消
     */
    private Integer status;

    /**
     * 配送状态：0未配送，1配送中，2已送达，3已完成
     */
    private Integer deliveryStatus;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付状态：0未支付，1已支付，2已退款
     */
    private Integer paymentStatus;

    /**
     * 收货人姓名
     */
    private String recipientName;

    /**
     * 收货人电话
     */
    private String recipientPhone;

    /**
     * 收货地址
     */
    private String recipientAddress;

    /**
     * 配送备注
     */
    private String deliveryNotes;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 配送方式：1外卖配送，2自取
     */
    private Integer deliveryType;

    /**
     * 取货人姓名（自取时使用）
     */
    private String pickupName;

    /**
     * 取货人电话（自取时使用）
     */
    private String pickupPhone;

    /**
     * 取货时间（自取时使用）
     */
    private String pickupTime;

    /**
     * 派送时间（外卖配送时使用）
     */
    @TableField("delivery_time")
    private String deliveryTime;

    /**
     * 支付时间
     */
    private LocalDateTime paidAt;

    /**
     * 发货时间
     */
    private LocalDateTime shippedAt;

    /**
     * 送达时间
     */
    private LocalDateTime arrivedAt;

    /**
     * 完成时间
     */
    private LocalDateTime deliveredAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
