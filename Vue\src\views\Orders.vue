<template>
  <div class="page-container">
    <div class="page-header">
      <h2 class="page-title">订单管理</h2>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar mb-16">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索订单号或用户"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
          >
            <el-option label="待支付" :value="1" />
            <el-option label="已支付" :value="2" />
            <el-option label="配送中" :value="3" />
            <el-option label="已完成" :value="4" />
            <el-option label="已取消" :value="5" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetAllFilters">
            重置筛选
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>

          <!-- 导出按钮 -->
          <el-dropdown @command="handleExport" class="export-dropdown">
            <el-button size="default" class="export-btn">
              <el-icon><Download /></el-icon>
              <span>导出</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="current">
                  <el-icon><DocumentCopy /></el-icon>
                  导出当前页
                </el-dropdown-item>
                <el-dropdown-item command="all">
                  <el-icon><FolderOpened /></el-icon>
                  导出全部数据
                </el-dropdown-item>
                <el-dropdown-item command="selected" divided>
                  <el-icon><Select /></el-icon>
                  导出已选择
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedOrders.length > 0" class="batch-actions">
      <div class="batch-info">
        <div class="batch-summary">
          <div class="summary-item">
            <el-icon><InfoFilled /></el-icon>
            <span class="summary-label">已选择:</span>
            <span class="summary-value">{{ selectedOrders.length }} 个订单</span>
          </div>
          <div class="summary-item">
            <el-icon><Money /></el-icon>
            <span class="summary-label">总金额:</span>
            <span class="summary-value">¥{{ selectedSummary.totalAmount }}</span>
          </div>
          <div class="summary-item">
            <el-icon><User /></el-icon>
            <span class="summary-label">用户数:</span>
            <span class="summary-value">{{ selectedSummary.userCount }} 个</span>
          </div>
          <div class="summary-item">
            <el-icon><Clock /></el-icon>
            <span class="summary-label">待支付:</span>
            <span class="summary-value">{{ selectedSummary.statusCounts.pending }}</span>
          </div>
          <div class="summary-item">
            <el-icon><CircleCheck /></el-icon>
            <span class="summary-label">已支付:</span>
            <span class="summary-value">{{ selectedSummary.statusCounts.paid }}</span>
          </div>
          <div class="summary-item">
            <el-icon><Van /></el-icon>
            <span class="summary-label">配送中:</span>
            <span class="summary-value">{{ selectedSummary.statusCounts.shipping }}</span>
          </div>
          <div class="summary-item">
            <el-icon><Select /></el-icon>
            <span class="summary-label">已完成:</span>
            <span class="summary-value">{{ selectedSummary.statusCounts.completed }}</span>
          </div>
          <div class="summary-item">
            <el-icon><CircleClose /></el-icon>
            <span class="summary-label">已取消:</span>
            <span class="summary-value">{{ selectedSummary.statusCounts.cancelled }}</span>
          </div>
        </div>
      </div>
      <div class="batch-buttons">
        <el-button
          type="success"
          size="small"
          @click="batchUpdateStatus(3)"
          :disabled="selectedSummary.statusCounts.paid === 0"
        >
          <el-icon><Van /></el-icon>
          批量发货 ({{ selectedSummary.statusCounts.paid }})
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="batchUpdateStatus(4)"
          :disabled="selectedSummary.statusCounts.shipping === 0"
        >
          <el-icon><Select /></el-icon>
          批量完成 ({{ selectedSummary.statusCounts.shipping }})
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="batchUpdateStatus(5)"
          :disabled="selectedSummary.statusCounts.pending === 0 && selectedSummary.statusCounts.paid === 0"
        >
          <el-icon><CircleClose /></el-icon>
          批量取消
        </el-button>
      </div>
    </div>

    <!-- 订单表格 -->
    <el-table
      :data="orders"
      :loading="loading"
      stripe
      style="width: 100%; min-width: 1100px"
      @selection-change="handleSelectionChange"
      size="small"
    >
      <!-- 多选列 -->
      <el-table-column type="selection" width="50" align="center" />

      <!-- 序号列 -->
      <el-table-column label="序号" width="70" align="center">
        <template #default="{ $index }">
          <span class="row-number">{{ (pagination.current - 1) * pagination.size + $index + 1 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="订单号" min-width="140" show-overflow-tooltip>
        <template #header>
          <div class="header-with-filter">
            <span>订单号</span>
            <el-popover placement="bottom" :width="200" trigger="click">
              <template #reference>
                <el-icon class="filter-icon">
                  <Filter />
                </el-icon>
              </template>
              <div class="filter-content">
                <el-input
                  v-model="searchForm.orderNo"
                  placeholder="输入订单号"
                  size="small"
                  clearable
                  @keyup.enter="handleOrderNoFilter"
                />
                <div class="filter-buttons">
                  <el-button size="small" @click="clearOrderNoFilter">重置</el-button>
                  <el-button size="small" type="primary" @click="handleOrderNoFilter">确定</el-button>
                </div>
              </div>
            </el-popover>
          </div>
        </template>
        <template #default="{ row }">
          <span class="order-no">{{ row.orderNo || row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="userName" label="用户" min-width="140" show-overflow-tooltip>
        <template #header>
          <div class="header-with-filter">
            <span>用户</span>
            <el-popover placement="bottom" :width="200" trigger="click">
              <template #reference>
                <el-icon class="filter-icon">
                  <Filter />
                </el-icon>
              </template>
              <div class="filter-content">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="输入用户名"
                  size="small"
                  clearable
                  @keyup.enter="handleUserFilter"
                />
                <div class="filter-buttons">
                  <el-button size="small" @click="clearUserFilter">重置</el-button>
                  <el-button size="small" type="primary" @click="handleUserFilter">确定</el-button>
                </div>
              </div>
            </el-popover>
          </div>
        </template>
        <template #default="{ row }">
          <div class="user-info">
            <el-avatar
              v-if="row.userAvatar"
              :src="row.userAvatar"
              :size="28"
              :fit="'cover'"
              class="user-avatar"
            />
            <el-avatar
              v-else
              :size="28"
              class="user-avatar"
            >
              {{ row.userName?.charAt(0) || 'U' }}
            </el-avatar>
            <span class="user-name">{{ row.userName || '未知用户' }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="recipientName" label="收货人" min-width="100" show-overflow-tooltip>
        <template #header>
          <div class="header-with-filter">
            <span>收货人</span>
            <el-popover placement="bottom" :width="200" trigger="click">
              <template #reference>
                <el-icon class="filter-icon">
                  <Filter />
                </el-icon>
              </template>
              <div class="filter-content">
                <el-input
                  v-model="searchForm.recipientName"
                  placeholder="输入收货人姓名"
                  size="small"
                  clearable
                  @keyup.enter="handleRecipientFilter"
                />
                <div class="filter-buttons">
                  <el-button size="small" @click="clearRecipientFilter">重置</el-button>
                  <el-button size="small" type="primary" @click="handleRecipientFilter">确定</el-button>
                </div>
              </div>
            </el-popover>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="recipientPhone" label="联系电话" min-width="120" show-overflow-tooltip>
        <template #header>
          <div class="header-with-filter">
            <span>联系电话</span>
            <el-popover placement="bottom" :width="200" trigger="click">
              <template #reference>
                <el-icon class="filter-icon">
                  <Filter />
                </el-icon>
              </template>
              <div class="filter-content">
                <el-input
                  v-model="searchForm.recipientPhone"
                  placeholder="输入手机号"
                  size="small"
                  clearable
                  @keyup.enter="handlePhoneFilter"
                />
                <div class="filter-buttons">
                  <el-button size="small" @click="clearPhoneFilter">重置</el-button>
                  <el-button size="small" type="primary" @click="handlePhoneFilter">确定</el-button>
                </div>
              </div>
            </el-popover>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="deliveryType" label="配送方式" min-width="100" align="center">
        <template #header>
          <div class="header-with-filter">
            <span>配送方式</span>
            <el-dropdown trigger="click" @command="handleDeliveryTypeFilter">
              <el-icon class="filter-icon">
                <Filter />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="">全部方式</el-dropdown-item>
                  <el-dropdown-item command="1">外卖配送</el-dropdown-item>
                  <el-dropdown-item command="2">到店自取</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        <template #default="{ row }">
          <el-tag :type="row.deliveryType === 1 ? 'primary' : 'success'" size="small">
            {{ row.deliveryType === 1 ? '外卖配送' : '到店自取' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="totalAmount" label="订单金额" min-width="100" align="right">
        <template #header>
          <div class="header-with-filter">
            <span>订单金额</span>
            <el-popover placement="bottom" :width="250" trigger="click">
              <template #reference>
                <el-icon class="filter-icon">
                  <Filter />
                </el-icon>
              </template>
              <div class="filter-content">
                <div class="amount-filter">
                  <el-input
                    v-model="searchForm.minAmount"
                    placeholder="最小金额"
                    size="small"
                    type="number"
                    clearable
                  />
                  <span class="amount-separator">-</span>
                  <el-input
                    v-model="searchForm.maxAmount"
                    placeholder="最大金额"
                    size="small"
                    type="number"
                    clearable
                  />
                </div>
                <div class="filter-buttons">
                  <el-button size="small" @click="clearAmountFilter">重置</el-button>
                  <el-button size="small" type="primary" @click="handleAmountFilter">确定</el-button>
                </div>
              </div>
            </el-popover>
          </div>
        </template>
        <template #default="{ row }">
          <span class="amount">¥{{ formatMoney(row.totalAmount) }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="当前状态" min-width="100" align="center">
        <template #header>
          <div class="header-with-filter">
            <span>当前状态</span>
            <el-dropdown trigger="click" @command="handleStatusFilter">
              <el-icon class="filter-icon">
                <Filter />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="">全部状态</el-dropdown-item>
                  <el-dropdown-item command="1">待付款</el-dropdown-item>
                  <el-dropdown-item command="2">已付款</el-dropdown-item>
                  <el-dropdown-item command="3">已发货</el-dropdown-item>
                  <el-dropdown-item command="4">已完成</el-dropdown-item>
                  <el-dropdown-item command="5">已取消</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        <template #default="{ row }">
          <el-tag :type="getOrderStatusType(row.status)" size="small">
            {{ getOrderStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="createdAt" label="下单时间" min-width="150" show-overflow-tooltip>
        <template #header>
          <div class="header-with-filter">
            <span>下单时间</span>
            <el-popover placement="bottom" :width="300" trigger="click">
              <template #reference>
                <el-icon class="filter-icon">
                  <Filter />
                </el-icon>
              </template>
              <div class="filter-content">
                <div class="date-filter">
                  <el-date-picker
                    v-model="searchForm.startDate"
                    type="date"
                    placeholder="开始日期"
                    size="small"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                  <span class="date-separator">至</span>
                  <el-date-picker
                    v-model="searchForm.endDate"
                    type="date"
                    placeholder="结束日期"
                    size="small"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                </div>
                <div class="filter-buttons">
                  <el-button size="small" @click="clearDateFilter">重置</el-button>
                  <el-button size="small" type="primary" @click="handleDateFilter">确定</el-button>
                </div>
              </div>
            </el-popover>
          </div>
        </template>
        <template #default="{ row }">
          {{ formatDate(row.createdAt, 'YYYY-MM-DD HH:mm') }}
        </template>
      </el-table-column>

      <el-table-column label="关键时间" min-width="180" show-overflow-tooltip>
        <template #header>
          <div class="header-with-filter">
            <span>关键时间</span>
            <el-dropdown trigger="click" @command="handleKeyTimeFilter">
              <el-icon class="filter-icon">
                <Filter />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="">全部订单</el-dropdown-item>
                  <el-dropdown-item command="paid">已支付</el-dropdown-item>
                  <el-dropdown-item command="shipped">已发货</el-dropdown-item>
                  <el-dropdown-item command="delivered">已完成</el-dropdown-item>
                  <el-dropdown-item command="pending">待处理</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        <template #default="{ row }">
          <div class="time-info">
            <!-- 支付时间 -->
            <div v-if="row.paidAt" class="time-item paid">
              <span class="time-text">💰 支付 {{ formatDate(row.paidAt, 'MM-DD HH:mm') }}</span>
            </div>
            <!-- 发货/配送时间 -->
            <div v-if="row.shippedAt" class="time-item shipped">
              <span class="time-text">🚚 {{ row.deliveryType === 1 ? '配送' : '准备' }} {{ formatDate(row.shippedAt, 'MM-DD HH:mm') }}</span>
            </div>
            <!-- 完成时间 -->
            <div v-if="row.deliveredAt" class="time-item delivered">
              <span class="time-text">✅ 完成 {{ formatDate(row.deliveredAt, 'MM-DD HH:mm') }}</span>
            </div>
            <!-- 期望配送时间 -->
            <div v-if="row.deliveryType === 1 && row.deliveryTime && !row.deliveredAt" class="time-item expected">
              <span class="time-text">⏰ {{ formatExpectedTime(row.deliveryTime) }}</span>
            </div>
            <!-- 期望取货时间 -->
            <div v-if="row.deliveryType === 2 && row.pickupTime && !row.deliveredAt" class="time-item expected">
              <span class="time-text">⏰ {{ formatExpectedTime(row.pickupTime) }}</span>
            </div>

            <!-- 无关键时间时的提示 -->
            <div v-if="!hasAnyTimeInfo(row)" class="time-item empty">
              <span class="no-time">待处理</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 备注列 -->
      <el-table-column label="备注" min-width="150">
        <template #header>
          <div class="header-with-filter">
            <span>备注</span>
            <el-dropdown trigger="click" @command="handleRemarkFilter">
              <el-icon class="filter-icon">
                <Filter />
              </el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="">全部订单</el-dropdown-item>
                  <el-dropdown-item command="hasRemark">有备注</el-dropdown-item>
                  <el-dropdown-item command="noRemark">无备注</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        <template #default="{ row }">
          <div class="remark-info">
            <!-- 订单备注 -->
            <div v-if="row.remark" class="remark-item order-remark">
              <div class="remark-content">{{ row.remark }}</div>
            </div>
            <!-- 配送备注 -->
            <div v-if="row.deliveryNotes" class="remark-item delivery-remark">
              <div class="remark-content delivery-note">{{ row.deliveryNotes }}</div>
            </div>
            <!-- 无备注时的提示 -->
            <div v-if="!row.remark && !row.deliveryNotes" class="no-remark">
              <span>-</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180" fixed="right" align="center">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
              class="action-btn"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>

            <el-dropdown
              v-if="row.status !== 4 && row.status !== 5"
              @command="(command) => handleOrderAction(row, command)"
              trigger="click"
            >
              <el-button type="success" size="small" class="action-btn">
                状态操作 <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>

                  <el-dropdown-item
                    v-if="row.status === 2"
                    command="3"
                  >
                    <el-icon><Van /></el-icon>
                    {{ row.deliveryType === 1 ? '开始配送' : '准备自取' }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="row.status === 3"
                    command="1"
                  >
                    <el-icon><Money /></el-icon>
                    确认送达
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="row.status === 1 && row.paymentStatus === 0"
                    command="4"
                  >
                    <el-icon><Select /></el-icon>
                    确认付款
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="row.status < 4"
                    command="5"
                    divided
                  >
                    <el-icon><CircleClose /></el-icon>
                    取消订单
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-tag
              v-else
              :type="row.status === 4 ? 'success' : 'danger'"
              size="small"
            >
              <el-icon><Lock /></el-icon>
              {{ row.status === 4 ? '已完成' : '已取消' }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Filter,
  Search,
  Refresh,
  View,
  ArrowDown,
  Money,
  Van,
  Select,
  CircleClose,
  Lock,
  InfoFilled,
  User,
  Clock,
  CircleCheck,
  Download,
  DocumentCopy,
  FolderOpened
} from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'
import { formatDate, formatMoney, getOrderStatusText, getOrderStatusType, exportToCSV } from '@/utils'

const router = useRouter()

const loading = ref(false)
const orders = ref([])
const selectedOrders = ref([])



// 格式化期望时间（显示完整年份）
const formatExpectedTime = (timeStr) => {
  if (!timeStr) return ''

  // 如果时间字符串包含年份，直接返回
  if (timeStr.includes('2024') || timeStr.includes('2025')) {
    return timeStr
  }

  // 如果是类似 "8月1日(周五) 09:00" 的格式，添加年份
  const currentYear = new Date().getFullYear()
  if (timeStr.includes('月') && timeStr.includes('日')) {
    return `${currentYear}年 ${timeStr}`
  }

  // 其他格式直接返回
  return timeStr
}

// 判断是否有任何时间信息
const hasAnyTimeInfo = (row) => {
  return row.paidAt || row.shippedAt || row.deliveredAt ||
         row.deliveryTime || row.pickupTime
}

// 筛选处理函数
const handleStatusFilter = (status) => {
  if (status === '') {
    // 显示全部状态
    searchForm.status = null
  } else {
    searchForm.status = parseInt(status)
  }
  pagination.current = 1
  loadOrders() // 状态筛选使用后端搜索，需要重新加载数据
}

const handleDeliveryTypeFilter = (deliveryType) => {
  if (deliveryType === '') {
    // 显示全部配送方式
    searchForm.deliveryType = null
  } else {
    searchForm.deliveryType = parseInt(deliveryType)
  }
  pagination.current = 1
  applyFilters() // 配送方式使用前端筛选
}

// 订单号筛选
const handleOrderNoFilter = () => {
  pagination.current = 1
  applyFilters()
}

const clearOrderNoFilter = () => {
  searchForm.orderNo = ''
  pagination.current = 1
  applyFilters()
}

// 用户筛选
const handleUserFilter = () => {
  pagination.current = 1
  loadOrders() // 用户筛选使用后端搜索，需要重新加载数据
}

const clearUserFilter = () => {
  searchForm.keyword = ''
  pagination.current = 1
  loadOrders() // 清除用户筛选，重新加载数据
}

// 收货人筛选
const handleRecipientFilter = () => {
  pagination.current = 1
  applyFilters()
}

const clearRecipientFilter = () => {
  searchForm.recipientName = ''
  pagination.current = 1
  applyFilters()
}

// 手机号筛选
const handlePhoneFilter = () => {
  pagination.current = 1
  applyFilters()
}

const clearPhoneFilter = () => {
  searchForm.recipientPhone = ''
  pagination.current = 1
  applyFilters()
}

// 金额筛选
const handleAmountFilter = () => {
  pagination.current = 1
  applyFilters()
}

const clearAmountFilter = () => {
  searchForm.minAmount = null
  searchForm.maxAmount = null
  pagination.current = 1
  applyFilters()
}

// 日期筛选
const handleDateFilter = () => {
  pagination.current = 1
  applyFilters()
}

const clearDateFilter = () => {
  searchForm.startDate = ''
  searchForm.endDate = ''
  pagination.current = 1
  applyFilters()
}

// 关键时间筛选
const handleKeyTimeFilter = (timeType) => {
  // 这里可以根据时间类型设置不同的状态筛选
  switch (timeType) {
    case 'paid':
      searchForm.status = 2
      break
    case 'shipped':
      searchForm.status = 3
      break
    case 'delivered':
      searchForm.status = 4
      break
    case 'pending':
      searchForm.status = 1
      break
    default:
      searchForm.status = null
  }
  pagination.current = 1
  loadOrders() // 关键时间筛选使用后端状态筛选
}

// 备注筛选
const handleRemarkFilter = (remarkType) => {
  if (remarkType === 'hasRemark') {
    searchForm.hasRemark = true
  } else if (remarkType === 'noRemark') {
    searchForm.hasRemark = false
  } else {
    searchForm.hasRemark = null
  }
  pagination.current = 1
  applyFilters() // 备注筛选使用前端筛选
}

const searchForm = reactive({
  keyword: '',
  status: null,
  deliveryType: null,
  recipientName: '',
  recipientPhone: '',
  orderNo: '',
  minAmount: null,
  maxAmount: null,
  startDate: '',
  endDate: '',
  hasRemark: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 计算选中订单的统计信息
const selectedSummary = computed(() => {
  if (selectedOrders.value.length === 0) {
    return {
      totalAmount: '0.00',
      userCount: 0,
      statusCounts: {
        pending: 0,
        paid: 0,
        shipping: 0,
        completed: 0,
        cancelled: 0
      }
    }
  }

  // 计算总金额
  const totalAmount = selectedOrders.value.reduce((sum, order) => {
    return sum + (parseFloat(order.totalAmount) || 0)
  }, 0)

  // 计算用户数量（去重）
  const userIds = new Set(selectedOrders.value.map(order => order.userId))
  const userCount = userIds.size

  // 计算各状态数量
  const statusCounts = {
    pending: selectedOrders.value.filter(order => order.status === 1).length,
    paid: selectedOrders.value.filter(order => order.status === 2).length,
    shipping: selectedOrders.value.filter(order => order.status === 3).length,
    completed: selectedOrders.value.filter(order => order.status === 4).length,
    cancelled: selectedOrders.value.filter(order => order.status === 5).length
  }

  return {
    totalAmount: totalAmount.toFixed(2),
    userCount,
    statusCounts
  }
})



// 原始订单数据（从后端获取的完整数据）
const allOrders = ref([])

// 加载订单列表
const loadOrders = async () => {
  loading.value = true
  try {
    // 构建后端查询参数（只使用后端支持的基础筛选）
    const params = {
      current: 1, // 获取所有数据，前端分页
      size: 1000, // 设置一个较大的值获取更多数据
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    // 清理空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    console.log('请求参数:', params)
    const response = await adminApi.getOrders(params)

    // 处理API响应数据
    if (response && response.data) {
      allOrders.value = response.data.records || []
      console.log('从后端获取订单数据:', allOrders.value.length, '条')
    } else {
      allOrders.value = []
    }

    // 应用前端筛选和分页
    applyFilters()

  } catch (error) {
    console.error('加载订单列表失败:', error)
    ElMessage.error('加载订单列表失败')
    allOrders.value = []
    orders.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}



// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadOrders()
}

// 重置所有筛选
const resetAllFilters = () => {
  // 重置搜索表单
  searchForm.keyword = ''
  searchForm.status = null
  searchForm.deliveryType = null
  searchForm.recipientName = ''
  searchForm.recipientPhone = ''
  searchForm.orderNo = ''
  searchForm.minAmount = null
  searchForm.maxAmount = null
  searchForm.startDate = ''
  searchForm.endDate = ''
  searchForm.hasRemark = null

  // 重置分页
  pagination.current = 1

  // 重新加载数据
  loadOrders()

  ElMessage.success('已重置所有筛选条件')
}

// 刷新页面数据
const handleRefresh = () => {
  // 保持当前搜索条件，重新加载数据
  loadOrders()

  ElMessage({
    message: '订单数据已刷新',
    type: 'success',
    duration: 1500,
    showClose: false
  })
}

// 查看订单详情
const handleViewDetail = (row) => {
  console.log('查看详情，订单ID:', row.id)
  try {
    router.push(`/orders/${row.id}`)
  } catch (error) {
    console.error('跳转详情页失败:', error)
    ElMessage.error('跳转详情页失败')
  }
}

// 应用前端筛选和分页
const applyFilters = () => {
  let filteredOrders = [...allOrders.value]

  // 订单号筛选（前端补充筛选，因为后端keyword可能不够精确）
  if (searchForm.orderNo) {
    filteredOrders = filteredOrders.filter(order =>
      (order.orderNo && order.orderNo.toLowerCase().includes(searchForm.orderNo.toLowerCase())) ||
      order.id.toString().includes(searchForm.orderNo)
    )
  }

  // 配送方式筛选
  if (searchForm.deliveryType !== null) {
    filteredOrders = filteredOrders.filter(order =>
      order.deliveryType === searchForm.deliveryType
    )
  }

  // 收货人筛选（前端补充筛选）
  if (searchForm.recipientName) {
    filteredOrders = filteredOrders.filter(order =>
      order.recipientName && order.recipientName.includes(searchForm.recipientName)
    )
  }

  // 手机号筛选（前端补充筛选）
  if (searchForm.recipientPhone) {
    filteredOrders = filteredOrders.filter(order =>
      order.recipientPhone && order.recipientPhone.includes(searchForm.recipientPhone)
    )
  }

  // 金额筛选
  if (searchForm.minAmount !== null || searchForm.maxAmount !== null) {
    filteredOrders = filteredOrders.filter(order => {
      const amount = parseFloat(order.totalAmount)
      const min = searchForm.minAmount ? parseFloat(searchForm.minAmount) : 0
      const max = searchForm.maxAmount ? parseFloat(searchForm.maxAmount) : Infinity
      return amount >= min && amount <= max
    })
  }

  // 日期筛选
  if (searchForm.startDate || searchForm.endDate) {
    filteredOrders = filteredOrders.filter(order => {
      const orderDate = new Date(order.createdAt).toISOString().split('T')[0]
      const start = searchForm.startDate || '1900-01-01'
      const end = searchForm.endDate || '2099-12-31'
      return orderDate >= start && orderDate <= end
    })
  }

  // 备注筛选
  if (searchForm.hasRemark !== null) {
    filteredOrders = filteredOrders.filter(order => {
      const hasRemark = !!(order.remark || order.deliveryNotes)
      return searchForm.hasRemark ? hasRemark : !hasRemark
    })
  }

  // 设置筛选后的总数
  pagination.total = filteredOrders.length

  // 前端分页
  const start = (pagination.current - 1) * pagination.size
  const end = start + pagination.size
  orders.value = filteredOrders.slice(start, end)

  console.log('筛选结果:', {
    原始数据: allOrders.value.length,
    筛选后: filteredOrders.length,
    当前页: orders.value.length,
    总页数: Math.ceil(pagination.total / pagination.size)
  })
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  applyFilters() // 重新应用筛选和分页
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  applyFilters() // 重新应用筛选和分页
}



// 更新订单状态
const updateOrderStatus = async (order, newStatus) => {
  const statusText = getOrderStatusText(newStatus)
  const deliveryTypeText = order.deliveryType === 1 ? '外卖配送' : '到店自取'

  // 构建详细的确认信息
  let confirmContent = `
    <div style="text-align: left; line-height: 1.6;">
      <p><strong>订单信息：</strong></p>
      <p>• 订单号：${order.id}</p>
      <p>• 用户：${order.userName || '未知用户'}</p>
      <p>• 收货人：${order.recipientName || '未设置'}</p>
      <p>• 配送方式：${deliveryTypeText}</p>
      <p>• 订单金额：¥${formatMoney(order.totalAmount)}</p>
      <p>• 当前状态：${getOrderStatusText(order.status)}</p>
      <br>
      <p><strong style="color: #e6a23c;">即将更新为：${statusText}</strong></p>
    </div>
  `

  // 根据状态添加特殊提醒
  if (newStatus === 5) {
    confirmContent += `
      <div style="color: #f56c6c; margin-top: 10px; padding: 10px; background: #fef0f0; border-radius: 4px;">
        <strong>⚠️ 注意：</strong>取消订单后将无法恢复，请确认操作！
      </div>
    `
  } else if (newStatus === 3) {
    confirmContent += `
      <div style="color: #409eff; margin-top: 10px; padding: 10px; background: #ecf5ff; border-radius: 4px;">
        <strong>🚚 提醒：</strong>确认商品已${order.deliveryType === 1 ? '开始配送' : '准备好自取'}？
      </div>
    `
  } else if (newStatus === 1) {
    confirmContent += `
      <div style="color: #e6a23c; margin-top: 10px; padding: 10px; background: #fdf6ec; border-radius: 4px;">
        <strong>📦 提醒：</strong>确认商品已送达客户？
      </div>
    `
  } else if (newStatus === 4) {
    confirmContent += `
      <div style="color: #67c23a; margin-top: 10px; padding: 10px; background: #f0f9ff; border-radius: 4px;">
        <strong>💰 提醒：</strong>确认客户已完成付款？
      </div>
    `
  }

  try {
    // 第一级确认
    await ElMessageBox.confirm(
      confirmContent,
      '订单状态更新确认',
      {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customClass: 'order-status-confirm-dialog'
      }
    )

    // 第二级确认（针对重要操作）
    if (newStatus === 5 || newStatus === 1 || newStatus === 4) {
      let secondConfirmText = ''
      let confirmType = 'warning'

      if (newStatus === 5) {
        secondConfirmText = '最后确认：真的要取消这个订单吗？'
        confirmType = 'error'
      } else if (newStatus === 1) {
        secondConfirmText = '最后确认：商品真的已经送达客户了吗？'
        confirmType = 'warning'
      } else if (newStatus === 4) {
        secondConfirmText = '最后确认：客户真的已经完成付款了吗？'
        confirmType = 'success'
      }

      await ElMessageBox.confirm(
        secondConfirmText,
        '二次确认',
        {
          confirmButtonText: '确定执行',
          cancelButtonText: '我再想想',
          type: confirmType,
          center: true
        }
      )
    }

    // 执行状态更新
    await adminApi.updateOrderStatus(order.id, newStatus)
    ElMessage.success({
      message: `订单状态已成功更新为"${statusText}"`,
      duration: 3000
    })
    loadOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新订单状态失败:', error)
      ElMessage.error('更新订单状态失败')
    }
  }
}

// 处理订单操作命令
const handleOrderAction = async (order, command) => {
  if (command === '4') {
    // 确认付款使用专门的方法
    await confirmPayment(order)
  } else {
    // 其他状态更新使用通用方法
    await updateOrderStatus(order, parseInt(command))
  }
}

// 确认付款（在订单列表中使用）
const confirmPayment = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确定要确认订单 ${order.id} 的客户已完成付款吗？确认后用户将可以确认收货。`,
      '确认付款',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApi.confirmPayment(order.id)
    ElMessage.success('付款确认成功，等待用户确认收货')
    loadOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认付款失败:', error)
      ElMessage.error('确认付款失败')
    }
  }
}

// 多选变化处理
const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

// 批量更新状态
const batchUpdateStatus = async (status) => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请先选择要操作的订单')
    return
  }

  // 根据目标状态筛选可操作的订单
  let targetOrders = []
  let actionText = ''

  switch (status) {
    case 3: // 批量发货
      targetOrders = selectedOrders.value.filter(order => order.status === 2)
      actionText = '发货'
      break
    case 4: // 批量完成
      targetOrders = selectedOrders.value.filter(order => order.status === 3)
      actionText = '完成'
      break
    case 5: // 批量取消
      targetOrders = selectedOrders.value.filter(order => order.status === 1 || order.status === 2)
      actionText = '取消'
      break
    default:
      ElMessage.error('不支持的操作')
      return
  }

  if (targetOrders.length === 0) {
    ElMessage.warning(`没有可${actionText}的订单`)
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要${actionText}选中的 ${targetOrders.length} 个订单吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量更新状态
    const promises = targetOrders.map(order =>
      adminApi.updateOrderStatus(order.id, status)
    )

    await Promise.all(promises)
    ElMessage.success(`批量${actionText}成功，共操作 ${targetOrders.length} 个订单`)
    selectedOrders.value = []
    loadOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

// 导出订单数据为CSV
const exportOrdersToCSV = (data, filename) => {
  // CSV头部
  const headers = ['订单号', '用户昵称', '用户电话', '订单状态', '配送方式', '订单金额', '收货人', '收货电话', '收货地址', '备注', '创建时间']

  // 数据映射函数
  const dataMapper = (order) => [
    order.orderNo || '',
    order.userNickname || '',
    order.userPhone || '',
    getOrderStatusText(order.status),
    order.deliveryType === 1 ? '配送' : '自提',
    `¥${formatMoney(order.totalAmount)}`,
    order.recipientName || '',
    order.recipientPhone || '',
    `${order.recipientProvince || ''}${order.recipientCity || ''}${order.recipientDistrict || ''}${order.recipientAddress || ''}`,
    order.remark || '',
    formatDate(order.createTime)
  ]

  exportToCSV(data, headers, filename, dataMapper)
}

// 导出处理
const handleExport = async (command) => {
  try {
    switch (command) {
      case 'current':
        if (orders.value.length === 0) {
          ElMessage.warning('当前页面没有订单数据')
          return
        }
        exportOrdersToCSV(orders.value, `订单数据_第${pagination.current}页_${new Date().toLocaleDateString()}.csv`)
        ElMessage.success(`已导出当前页 ${orders.value.length} 条订单数据`)
        break
      case 'all':
        ElMessage.info('正在导出全部数据，请稍候...')
        try {
          // 获取所有订单数据
          const response = await adminApi.getOrders({
            current: 1,
            size: 10000, // 获取大量数据
            keyword: searchForm.keyword,
            status: searchForm.status
          })
          const allOrders = response.data.records || []
          if (allOrders.length === 0) {
            ElMessage.warning('没有订单数据可导出')
            return
          }
          exportOrdersToCSV(allOrders, `全部订单数据_${new Date().toLocaleDateString()}.csv`)
          ElMessage.success(`全部订单数据导出完成，共 ${allOrders.length} 条`)
        } catch (error) {
          console.error('导出全部数据失败:', error)
          ElMessage.error('导出全部数据失败')
        }
        break
      case 'selected':
        if (selectedOrders.value.length === 0) {
          ElMessage.warning('请先选择要导出的订单')
          return
        }
        exportOrdersToCSV(selectedOrders.value, `选中订单数据_${selectedOrders.value.length}条_${new Date().toLocaleDateString()}.csv`)
        ElMessage.success(`已导出选中的 ${selectedOrders.value.length} 条订单数据`)
        break
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}

onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.search-bar {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  min-width: 60px;
}

/* 导出按钮样式 */
.export-dropdown {
  margin-left: 8px;
}

.export-btn {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.export-btn:hover {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.dropdown-icon {
  margin-left: 4px;
  transition: transform 0.3s ease;
}

.export-dropdown.is-opened .dropdown-icon {
  transform: rotate(180deg);
}

/* 序号样式 */
.row-number {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

/* 批量操作栏样式 */
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.batch-info {
  flex: 1;
}

.batch-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #0369a1;
  font-size: 14px;
}

.summary-label {
  font-weight: 500;
  color: #0f172a;
}

.summary-value {
  font-weight: 600;
  color: #0369a1;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.batch-buttons .el-button {
  margin: 0;
  min-width: 100px;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 4px 0;
}

.user-avatar {
  flex-shrink: 0;
  border-radius: 50% !important;
  border: 1px solid #e4e7ed;
}

.user-avatar img {
  border-radius: 50% !important;
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
}

.user-name {
  font-size: 13px;
  color: #606266;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

/* 金额样式 */
.amount {
  font-weight: 600;
  color: #e6a23c;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-height: 20px;
  padding: 2px 0;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 11px;
  line-height: 1.3;
  white-space: nowrap;
}

.time-text {
  font-size: 11px;
  font-weight: 500;
}

.time-detail {
  font-size: 10px;
  opacity: 0.8;
  margin-left: 4px;
}

/* 不同时间类型的颜色 */
.time-item.paid {
  color: #e6a23c;
}

.time-item.shipped {
  color: #409eff;
}

.time-item.delivered {
  color: #67c23a;
}

.time-item.expected {
  color: #909399;
}

/* 备注列样式 */
.remark-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
  min-height: 20px;
  padding: 2px 0;
}

.remark-item {
  font-size: 11px;
  line-height: 1.4;
}

.remark-content {
  color: #606266;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  max-width: 100%;
}

.remark-content.delivery-note {
  color: #409eff;
  font-style: italic;
}

.no-remark {
  color: #c0c4cc;
  font-size: 11px;
  text-align: center;
}

/* 操作按钮样式优化 */
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.action-btn {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 60px;
}

/* 表格整体样式优化 */
:deep(.el-table .el-table__cell) {
  padding: 8px 6px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table .el-table__header-wrapper .el-table__header .el-table__cell) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
  font-size: 12px;
}

:deep(.el-table .el-table__row:hover .el-table__cell) {
  background-color: #f5f7fa;
}

/* 固定列样式 */
:deep(.el-table .el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.06);
}

.time-item.empty {
  color: #c0c4cc;
  font-style: italic;
}

.no-time {
  font-size: 11px;
}

/* 订单号样式 */
.order-no {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #409eff;
  font-size: 12px;
}

/* 表头筛选样式 */
.header-with-filter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  width: 100%;
}

.filter-icon {
  font-size: 14px;
  color: #909399;
  cursor: pointer;
  margin-left: 4px;
  transition: color 0.3s;
}

.filter-icon:hover {
  color: #409eff;
}

/* 筛选弹窗内容样式 */
.filter-content {
  padding: 8px 0;
}

.filter-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
}

/* 金额筛选样式 */
.amount-filter {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.amount-separator {
  color: #909399;
  font-size: 12px;
}

/* 日期筛选样式 */
.date-filter {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
}

.date-separator {
  color: #909399;
  font-size: 12px;
  text-align: center;
  margin: 4px 0;
}

:deep(.el-table .el-table__header-wrapper) {
  background: #f8f9fa;
}

:deep(.el-table .el-table__header th) {
  background: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

/* 头像全局样式优化 */
:deep(.el-avatar) {
  border-radius: 50% !important;
}

:deep(.el-avatar img) {
  border-radius: 50% !important;
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
}

/* 状态确认对话框样式 */
:deep(.order-status-confirm-dialog) {
  width: 500px;
}

:deep(.order-status-confirm-dialog .el-message-box__content) {
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .batch-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .batch-summary {
    justify-content: center;
  }

  .batch-buttons {
    justify-content: center;
  }

  :deep(.order-status-confirm-dialog) {
    width: 90%;
    max-width: 500px;
  }
}

@media (max-width: 768px) {
  .batch-summary {
    gap: 12px;
  }

  .summary-item {
    font-size: 13px;
  }

  .batch-buttons {
    flex-direction: column;
  }

  .batch-buttons .el-button {
    min-width: auto;
  }

  .user-info {
    flex-direction: column;
    gap: 4px;
  }

  .user-name {
    max-width: none;
  }

  .time-info {
    gap: 1px;
  }

  .time-item {
    font-size: 11px;
  }
}
</style>
