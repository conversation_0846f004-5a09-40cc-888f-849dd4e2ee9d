<!-- pages/profile/profile.wxml -->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section card">
    <view class="user-info" wx:if="{{userInfo}}" bindtap="editUserInfo">
      <view class="avatar-container">
        <image class="user-avatar" src="{{currentAvatarUrl}}" mode="aspectFill" binderror="onAvatarError" bindlongpress="resetOrderBadgeReadStatus" />
        <view class="avatar-edit-icon">✏️</view>
      </view>
      <view class="user-details">
        <text class="user-name">{{currentNickname}}</text>
        <text class="user-phone">{{currentPhone}}</text>
      </view>
      <text class="arrow-icon">></text>
    </view>

    <view class="login-prompt" wx:else bindtap="goToLogin">
      <image class="default-avatar" src="/images/default-avatar.png" mode="aspectFill" />
      <view class="login-info">
        <text class="login-text">点击登录</text>
        <text class="login-desc">登录后享受更多服务</text>
      </view>
      <text class="arrow-icon">></text>
    </view>
  </view>

  <!-- 订单管理 -->
  <view class="order-section card">
    <view class="section-header">
      <text class="section-title">我的订单</text>
      <text class="section-more" bindtap="goToOrders">查看全部 ></text>
    </view>
    
    <view class="order-types">
      <view class="order-type" bindtap="goToOrders" data-status="pending">
        <view class="type-icon">💰</view>
        <text class="type-name">待付款</text>
        <view class="type-badge" wx:if="{{orderCounts.pending > 0}}">{{orderCounts.pending}}</view>
      </view>
      <view class="order-type" bindtap="goToOrders" data-status="ordered">
        <view class="type-icon">📦</view>
        <text class="type-name">待发货</text>
        <view class="type-badge" wx:if="{{orderCounts.ordered > 0}}">{{orderCounts.ordered}}</view>
      </view>
      <view class="order-type" bindtap="goToOrders" data-status="shipping">
        <view class="type-icon">🚚</view>
        <text class="type-name">配送中</text>
        <view class="type-badge" wx:if="{{orderCounts.shipping > 0}}">{{orderCounts.shipping}}</view>
      </view>
      <view class="order-type" bindtap="goToOrders" data-status="toReceive">
        <view class="type-icon">📋</view>
        <text class="type-name">待收货</text>
        <view class="type-badge" wx:if="{{orderCounts.toReceive > 0}}">{{orderCounts.toReceive}}</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section card">
    <view class="menu-item" bindtap="goToFavorites">
      <view class="menu-icon">❤️</view>
      <text class="menu-name">我的收藏</text>
      <text class="arrow-icon">></text>
    </view>
    
    <view class="menu-item" bindtap="goToAddress">
      <view class="menu-icon">📍</view>
      <text class="menu-name">收货地址</text>
      <text class="arrow-icon">></text>
    </view>
    
    <view class="menu-item" bindtap="goToService">
      <view class="menu-icon">🎧</view>
      <text class="menu-name">客服中心</text>
      <text class="arrow-icon">></text>
    </view>
    
    <view class="menu-item" bindtap="goToAbout">
      <view class="menu-icon">ℹ️</view>
      <text class="menu-name">关于我们</text>
      <text class="arrow-icon">></text>
    </view>
  </view>



  <!-- 设置区域 -->
  <view class="settings-section card">
    <view class="menu-item" bindtap="showCacheModal">
      <view class="menu-icon">🗑️</view>
      <text class="menu-name">缓存管理</text>
      <text class="cache-size">{{cacheSize}}</text>
      <text class="arrow-icon">></text>
    </view>

    <view class="menu-item" wx:if="{{userInfo}}" bindtap="logout">
      <view class="menu-icon">🚪</view>
      <text class="menu-name">退出登录</text>
      <text class="arrow-icon">></text>
    </view>
  </view>

  <!-- 缓存管理弹窗 -->
  <view class="modal-overlay" wx:if="{{showCacheModal}}" bindtap="hideCacheModal">
    <view class="cache-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">缓存管理</text>
        <view class="close-btn" bindtap="hideCacheModal">×</view>
      </view>

      <view class="cache-info">
        <view class="info-item">
          <text class="info-label">缓存大小</text>
          <text class="info-value">{{cacheInfo.sizeText}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">缓存项数</text>
          <text class="info-value">{{cacheInfo.keyCount}}个</text>
        </view>
      </view>

      <view class="cache-actions">
        <view class="action-item" bindtap="clearExpiredCache">
          <view class="action-icon">⏰</view>
          <view class="action-content">
            <text class="action-title">清除过期缓存</text>
            <text class="action-desc">清除已过期的临时数据</text>
          </view>
        </view>

        <view class="action-item" bindtap="clearImageCache">
          <view class="action-icon">🖼️</view>
          <view class="action-content">
            <text class="action-title">清除图片缓存</text>
            <text class="action-desc">清除本地存储的图片文件</text>
          </view>
        </view>

        <view class="action-item" bindtap="clearUserDataCache">
          <view class="action-icon">👤</view>
          <view class="action-content">
            <text class="action-title">清除用户数据</text>
            <text class="action-desc">清除用户信息和浏览记录</text>
          </view>
        </view>

        <view class="action-item danger" bindtap="clearAllCache">
          <view class="action-icon">🗑️</view>
          <view class="action-content">
            <text class="action-title">清除所有缓存</text>
            <text class="action-desc">清除所有本地存储数据</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">妙星猫 v1.0.0</text>
  </view>
</view>
