<!-- pages/category/category.wxml -->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar" bindtap="goToSearch">
    <view class="search-input">
      <text class="search-icon">🔍</text>
      <text class="search-placeholder">搜索你喜欢的花卉</text>
    </view>
  </view>

  <view class="content">
    <!-- 分类侧边栏 -->
    <view class="category-sidebar">
      <!-- 模式切换按钮 -->
      <view class="mode-switch">
        <view class="switch-item {{viewMode === 'category' ? 'active' : ''}}"
              bindtap="switchViewMode"
              data-mode="category">
          <text class="switch-text">内容分类</text>
        </view>
        <view class="switch-item {{viewMode === 'price' ? 'active' : ''}}"
              bindtap="switchViewMode"
              data-mode="price">
          <text class="switch-text">价格分类</text>
        </view>
      </view>

      <!-- 内容分类列表 -->
      <view class="category-list" wx:if="{{viewMode === 'category'}}">
        <view class="category-item {{currentCategoryId === item.id ? 'active' : ''}}"
              wx:for="{{categories}}"
              wx:key="id"
              bindtap="selectCategory"
              data-id="{{item.id}}">
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>

      <!-- 价格分类列表 -->
      <view class="price-category-list" wx:if="{{viewMode === 'price'}}">
        <view class="price-category-item {{!selectedPriceCategory ? 'active' : ''}}"
              bindtap="selectPriceCategory"
              data-id="all">
          <text class="price-category-name">全部价格</text>
          <text class="price-range">所有价位</text>
        </view>
        <view class="price-category-item {{selectedPriceCategory && selectedPriceCategory.id === item.id ? 'active' : ''}}"
              wx:for="{{priceCategories}}"
              wx:key="id"
              bindtap="selectPriceCategory"
              data-id="{{item.id}}">
          <text class="price-category-name">{{item.name}}</text>
          <text class="price-range">¥{{item.minPrice}}{{item.maxPrice >= 99999 ? '+' : '-¥' + item.maxPrice}}</text>
        </view>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="product-content">
      <!-- 分类信息 -->
      <view class="category-header" wx:if="{{viewMode === 'category' && currentCategory}}">
        <image class="category-image" src="{{currentCategory.imageUrl}}" mode="aspectFill" />
        <view class="category-info">
          <text class="category-title">{{currentCategory.name}}</text>
          <text class="category-desc">{{currentCategory.description}}</text>
        </view>
      </view>

      <!-- 价格分类信息 -->
      <view class="price-header" wx:if="{{viewMode === 'price'}}">
        <view class="price-info">
          <text class="price-title">
            {{selectedPriceCategory ? selectedPriceCategory.name : '全部价格'}}
          </text>
          <text class="price-desc" wx:if="{{selectedPriceCategory}}">
            ¥{{selectedPriceCategory.minPrice}}{{selectedPriceCategory.maxPrice >= 99999 ? '以上' : ' - ¥' + selectedPriceCategory.maxPrice}}
          </text>
          <text class="price-desc" wx:else>所有价位的精美花卉</text>
        </view>
      </view>

      <!-- 精选商品区域 -->
      <view class="featured-section" wx:if="{{featuredFlowers.length > 0}}">
        <view class="section-header">
          <text class="section-title">✨ 精选推荐</text>
          <text class="section-subtitle" wx:if="{{viewMode === 'category' && currentCategory}}">{{currentCategory.name}}分类精选</text>
          <text class="section-subtitle" wx:elif="{{viewMode === 'price' && selectedPriceCategory}}">{{selectedPriceCategory.name}}精选</text>
          <text class="section-subtitle" wx:else>精选商品推荐</text>
        </view>
        <view class="featured-grid">
          <view class="featured-item" wx:for="{{featuredFlowers}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
            <image class="featured-image" src="{{item.mainImage}}" mode="aspectFill" />
            <view class="featured-badge">精选</view>
            <view class="featured-info">
              <text class="featured-name">{{item.name}}</text>
              <text class="featured-desc">{{item.description}}</text>
              <view class="featured-price">
                <text class="price price-medium">¥{{item.price}}</text>
                <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
              </view>
              <view class="featured-tags">
                <text class="tag tag-featured" wx:for="{{item.tagList}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
              </view>
              <view class="featured-footer">
                <text class="sales-count">已售{{item.salesCount}}件</text>
                <view class="add-cart-btn" bindtap="addToCart" data-id="{{item.id}}" catchtap="true">
                  <text class="cart-icon">🛒</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 全部商品区域 -->
      <view class="all-products-section" wx:if="{{flowers.length > 0}}">
        <view class="section-header">
          <text class="section-title">全部商品</text>
          <text class="section-subtitle" wx:if="{{viewMode === 'category' && currentCategory}}">更多{{currentCategory.name}}商品</text>
          <text class="section-subtitle" wx:elif="{{viewMode === 'price' && selectedPriceCategory}}">更多{{selectedPriceCategory.name}}商品</text>
          <text class="section-subtitle" wx:else>更多精美商品</text>
        </view>
        <view class="product-grid">
          <view class="product-item" wx:for="{{flowers}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
            <image class="product-image" src="{{item.mainImage}}" mode="aspectFill" />
            <view class="product-info">
              <text class="product-name">{{item.name}}</text>
              <text class="product-desc">{{item.description}}</text>
              <view class="product-price">
                <text class="price price-medium">¥{{item.price}}</text>
                <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
              </view>
              <view class="product-tags">
                <text class="tag" wx:for="{{item.tagList}}" wx:key="index" wx:for-item="tag">{{tag}}</text>
              </view>
              <view class="product-footer">
                <text class="sales-count">已售{{item.salesCount}}件</text>
                <view class="add-cart-btn" bindtap="addToCart" data-id="{{item.id}}" catchtap="true">
                  <text class="cart-icon">🛒</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-more" wx:if="{{isLoading}}">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && flowers.length > 0}}">
        <text class="no-more-text">没有更多商品了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{featuredFlowers.length === 0 && flowers.length === 0 && !isLoading && featuredLoaded}}">
        <view class="empty-icon">🌸</view>
        <text class="empty-text">暂无商品</text>
        <text class="empty-desc">该分类下还没有商品</text>
      </view>
    </view>
  </view>
</view>
