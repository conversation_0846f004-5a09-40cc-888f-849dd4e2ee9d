<template>
  <div class="page-container">
    <div class="page-header">
      <h2 class="page-title">订单详情</h2>
      <el-button @click="$router.go(-1)">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else-if="order" class="order-detail">
      <!-- 订单基本信息 -->
      <el-card class="mb-16">
        <template #header>
          <div class="card-header">
            <span>订单信息</span>
            <el-tag :type="getOrderStatusType(order.status)">
              {{ getOrderStatusText(order.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">
            {{ order.orderNo || order.id }}
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusType(order.status)">
              {{ getOrderStatusText(order.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户信息">
            <div class="user-detail">
              <el-avatar
                v-if="order.userAvatar"
                :src="order.userAvatar"
                :size="36"
                :fit="'cover'"
                class="detail-user-avatar"
              />
              <el-avatar
                v-else
                :size="36"
                class="detail-user-avatar"
              >
                {{ order.userName?.charAt(0) || 'U' }}
              </el-avatar>
              <div class="user-text">
                <div class="user-name">{{ order.userName || '未知用户' }}</div>
                <div class="user-phone">{{ order.userPhone || '未绑定手机' }}</div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="配送方式">
            <el-tag :type="order.deliveryType === 1 ? 'primary' : 'success'">
              {{ order.deliveryType === 1 ? '外卖配送' : '到店自取' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单金额">
            <div class="amount-detail">
              <div>总金额：¥{{ formatMoney(order.totalAmount) }}</div>
              <div v-if="order.discountAmount && order.discountAmount > 0" class="discount">
                优惠：-¥{{ formatMoney(order.discountAmount) }}
              </div>
              <div class="final-amount">实付：¥{{ formatMoney(order.finalAmount || order.totalAmount) }}</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="支付信息">
            <div class="payment-info">
              <div>支付方式：{{ order.paymentMethod || '未支付' }}</div>
              <div>支付状态：{{ order.paymentStatus === 1 ? '已支付' : '未支付' }}</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="下单时间">
            {{ formatDate(order.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">
            {{ order.paidAt ? formatDate(order.paidAt) : '未支付' }}
          </el-descriptions-item>
          <el-descriptions-item label="发货时间">
            {{ order.shippedAt ? formatDate(order.shippedAt) : '未发货' }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ order.deliveredAt ? formatDate(order.deliveredAt) : '未完成' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ order.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 配送信息 -->
      <el-card class="mb-16">
        <template #header>
          <div class="card-header">
            <span>配送信息</span>
            <el-tag :type="order.deliveryType === 1 ? 'primary' : 'success'">
              {{ order.deliveryType === 1 ? '外卖配送' : '到店自取' }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <!-- 外卖配送信息 -->
          <template v-if="order.deliveryType === 1">
            <el-descriptions-item label="收货人">
              {{ order.recipientName || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ order.recipientPhone || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="收货地址" :span="2">
              {{ order.recipientAddress || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="期望送达时间" :span="2">
              {{ order.deliveryTime || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="配送备注" :span="2">
              {{ order.deliveryNotes || '无' }}
            </el-descriptions-item>
          </template>

          <!-- 自取信息 -->
          <template v-else>
            <el-descriptions-item label="取货人">
              {{ order.pickupName || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="取货电话">
              {{ order.pickupPhone || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="期望取货时间" :span="2">
              {{ order.pickupTime || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="店铺地址" :span="2">
              <div class="store-address">
                <el-icon><Location /></el-icon>
                <span>花店地址：请联系商家确认具体取货地址</span>
              </div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </el-card>

      <!-- 商品信息 -->
      <el-card class="mb-16">
        <template #header>
          <span>商品信息</span>
        </template>
        
        <el-table :data="order.items" stripe>
          <el-table-column label="商品图片" width="100">
            <template #default="{ row }">
              <el-image
                :src="row.flowerImage"
                :preview-src-list="[row.flowerImage]"
                style="width: 60px; height: 60px"
                fit="cover"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="flowerName" label="商品名称" />
          
          <el-table-column prop="price" label="单价">
            <template #default="{ row }">
              ¥{{ formatMoney(row.price) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="quantity" label="数量" />
          
          <el-table-column label="小计">
            <template #default="{ row }">
              ¥{{ formatMoney(row.price * row.quantity) }}
            </template>
          </el-table-column>
        </el-table>
        
        <div class="order-summary">
          <div class="summary-item">
            <span>商品总额：</span>
            <span>¥{{ formatMoney(order.totalAmount) }}</span>
          </div>
          <div class="summary-item total">
            <span>订单总额：</span>
            <span class="total-amount">¥{{ formatMoney(order.totalAmount) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <el-card>
        <template #header>
          <span>订单操作</span>
        </template>

        <div class="order-actions">
          <!-- 状态操作按钮 - 正确的订单流程：已下单 -> 配送中 -> 待付款 -> 已完成 -->
          <template v-if="order.status !== 4 && order.status !== 5">
            <el-button
              v-if="order.status === 2"
              type="success"
              @click="updateOrderStatus(3)"
            >
              <el-icon><Van /></el-icon>
              开始配送
            </el-button>

            <el-button
              v-if="order.status === 3"
              type="primary"
              @click="updateOrderStatus(1)"
            >
              <el-icon><Money /></el-icon>
              确认送达
            </el-button>

            <el-button
              v-if="order.status === 1 && order.paymentStatus === 0"
              type="success"
              @click="confirmPayment"
            >
              <el-icon><Select /></el-icon>
              确认付款
            </el-button>

            <el-button
              v-if="order.status < 4"
              type="danger"
              @click="updateOrderStatus(5)"
            >
              <el-icon><CircleClose /></el-icon>
              取消订单
            </el-button>
          </template>

          <!-- 修改按钮 -->
          <el-button
            type="warning"
            @click="handleEditOrder"
          >
            <el-icon><Edit /></el-icon>
            修改订单
          </el-button>
        </div>
      </el-card>

      <!-- 底部返回按钮 -->
      <div class="bottom-actions">
        <el-button type="primary" @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回上一页
        </el-button>
        <el-button @click="$router.push('/orders')">
          <el-icon><List /></el-icon>
          返回订单列表
        </el-button>
      </div>
    </div>

    <div v-else class="error-container">
      <el-result
        icon="error"
        title="订单不存在"
        sub-title="请检查订单号是否正确"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/orders')">
            返回订单列表
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Money,
  Van,
  Select,
  CircleClose,
  Edit,
  List
} from '@element-plus/icons-vue'
import { adminApi } from '@/api/admin'
import { formatDate, formatMoney, getOrderStatusText, getOrderStatusType } from '@/utils'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const order = ref(null)

// 加载订单详情
const loadOrderDetail = async () => {
  loading.value = true
  try {
    const response = await adminApi.getOrderDetail(route.params.id)
    order.value = response.data
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  } finally {
    loading.value = false
  }
}

// 更新订单状态
const updateOrderStatus = async (newStatus) => {
  const statusText = getOrderStatusText(newStatus)
  
  try {
    await ElMessageBox.confirm(
      `确定要将订单状态更新为 "${statusText}" 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await adminApi.updateOrderStatus(order.value.id, newStatus)
    ElMessage.success('状态更新成功')
    
    // 重新加载订单详情
    loadOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新订单状态失败:', error)
      ElMessage.error('更新订单状态失败')
    }
  }
}

// 确认付款
const confirmPayment = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要确认客户已完成付款吗？确认后用户将可以确认收货。',
      '确认付款',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await adminApi.confirmPayment(order.value.id)
    ElMessage.success('付款确认成功，等待用户确认收货')

    // 重新加载订单详情
    loadOrderDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认付款失败:', error)
      ElMessage.error('确认付款失败')
    }
  }
}

// 修改订单
const handleEditOrder = () => {
  ElMessageBox.confirm(
    '确定要修改此订单吗？修改后需要重新确认订单信息。',
    '修改订单确认',
    {
      confirmButtonText: '确定修改',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 显示订单修改选项
    showEditOptions()
  }).catch(() => {
    // 用户取消操作
  })
}

// 显示编辑选项
const showEditOptions = () => {
  const editOptions = [
    '修改收货信息',
    '修改配送方式',
    '修改订单备注',
    '修改订单金额'
  ]

  ElMessageBox.confirm(
    `请选择要修改的内容：\n\n${editOptions.map((option, index) => `${index + 1}. ${option}`).join('\n')}\n\n注意：修改订单可能会影响配送时间和费用`,
    '选择修改内容',
    {
      confirmButtonText: '继续修改',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    // 实际项目中这里可以打开编辑对话框或跳转到编辑页面
    ElMessage.info('订单修改功能开发中，请联系管理员手动修改')
  }).catch(() => {
    // 用户取消
  })
}

onMounted(() => {
  loadOrderDetail()
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.summary-item {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-item.total {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.total-amount {
  color: #f56c6c;
}

.order-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 底部操作按钮样式 */
.bottom-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
  border-radius: 4px;
}

.mb-16 {
  margin-bottom: 16px;
}

/* 用户详情样式 */
.user-detail {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-user-avatar {
  flex-shrink: 0;
  border-radius: 50% !important;
  border: 1px solid #e4e7ed;
}

.detail-user-avatar img {
  border-radius: 50% !important;
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
}

.user-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #303133;
}

.user-phone {
  font-size: 12px;
  color: #909399;
}

/* 金额详情样式 */
.amount-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.discount {
  color: #e6a23c;
  font-size: 13px;
}

.final-amount {
  font-weight: 600;
  color: #f56c6c;
  font-size: 14px;
}

/* 支付信息样式 */
.payment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
}

/* 店铺地址样式 */
.store-address {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
}

.store-address .el-icon {
  color: #409eff;
}

/* 头像全局样式优化 */
:deep(.el-avatar) {
  border-radius: 50% !important;
}

:deep(.el-avatar img) {
  border-radius: 50% !important;
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-detail {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .order-actions {
    flex-direction: column;
  }

  .order-actions .el-button {
    width: 100%;
  }

  .bottom-actions {
    flex-direction: column;
    gap: 12px;
  }

  .bottom-actions .el-button {
    width: 100%;
  }
}
</style>
