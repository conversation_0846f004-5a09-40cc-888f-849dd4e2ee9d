package com.flower.service;

import com.flower.util.CaptchaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务
 */
@Slf4j
@Service
public class CaptchaService {
    
    // 验证码存储，实际项目中建议使用Redis
    private final Map<String, CaptchaInfo> captchaStore = new ConcurrentHashMap<>();
    
    // 验证码有效期（分钟）
    private static final int CAPTCHA_EXPIRE_MINUTES = 5;
    
    // 定时清理过期验证码
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    public CaptchaService() {
        // 每分钟清理一次过期验证码
        scheduler.scheduleAtFixedRate(this::cleanExpiredCaptcha, 1, 1, TimeUnit.MINUTES);
    }
    
    /**
     * 生成验证码
     */
    public CaptchaResponse generateCaptcha() {
        try {
            // 生成验证码
            CaptchaUtil.CaptchaResult result = CaptchaUtil.generateCaptcha();
            
            // 生成唯一标识
            String captchaId = UUID.randomUUID().toString();
            
            // 存储验证码信息
            CaptchaInfo info = new CaptchaInfo(result.getCode(), System.currentTimeMillis());
            captchaStore.put(captchaId, info);
            
            log.info("生成验证码成功，ID: {}, 验证码: {}", captchaId, result.getCode());
            
            return new CaptchaResponse(captchaId, result.getImage());
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            throw new RuntimeException("生成验证码失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证验证码
     */
    public boolean verifyCaptcha(String captchaId, String userInput) {
        try {
            if (captchaId == null || userInput == null) {
                log.warn("验证码ID或用户输入为空");
                return false;
            }
            
            CaptchaInfo info = captchaStore.get(captchaId);
            if (info == null) {
                log.warn("验证码不存在或已过期，ID: {}", captchaId);
                return false;
            }
            
            // 检查是否过期
            long now = System.currentTimeMillis();
            if (now - info.getCreateTime() > CAPTCHA_EXPIRE_MINUTES * 60 * 1000) {
                log.warn("验证码已过期，ID: {}", captchaId);
                captchaStore.remove(captchaId);
                return false;
            }
            
            // 验证码验证（不区分大小写）
            boolean isValid = userInput.equalsIgnoreCase(info.getCode());
            
            // 验证后删除验证码（一次性使用）
            captchaStore.remove(captchaId);
            
            log.info("验证码验证结果，ID: {}, 用户输入: {}, 正确答案: {}, 结果: {}", 
                    captchaId, userInput, info.getCode(), isValid);
            
            return isValid;
        } catch (Exception e) {
            log.error("验证验证码失败", e);
            return false;
        }
    }
    
    /**
     * 清理过期验证码
     */
    private void cleanExpiredCaptcha() {
        try {
            long now = System.currentTimeMillis();
            long expireTime = CAPTCHA_EXPIRE_MINUTES * 60 * 1000;
            
            captchaStore.entrySet().removeIf(entry -> {
                boolean expired = now - entry.getValue().getCreateTime() > expireTime;
                if (expired) {
                    log.debug("清理过期验证码，ID: {}", entry.getKey());
                }
                return expired;
            });
        } catch (Exception e) {
            log.error("清理过期验证码失败", e);
        }
    }
    
    /**
     * 验证码信息
     */
    private static class CaptchaInfo {
        private final String code;
        private final long createTime;
        
        public CaptchaInfo(String code, long createTime) {
            this.code = code;
            this.createTime = createTime;
        }
        
        public String getCode() {
            return code;
        }
        
        public long getCreateTime() {
            return createTime;
        }
    }
    
    /**
     * 验证码响应
     */
    public static class CaptchaResponse {
        private final String captchaId;
        private final String image;
        
        public CaptchaResponse(String captchaId, String image) {
            this.captchaId = captchaId;
            this.image = image;
        }
        
        public String getCaptchaId() {
            return captchaId;
        }
        
        public String getImage() {
            return image;
        }
    }
}
