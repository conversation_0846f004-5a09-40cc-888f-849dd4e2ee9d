package com.flower.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.entity.*;
import com.flower.mapper.*;
import com.flower.service.OrderService;
import com.flower.service.UserPickupInfoService;
import com.flower.entity.UserPickupInfo;
import com.flower.websocket.NotificationWebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单通知数据类
 */
class OrderNotificationData {
    private Long id;
    private String orderNo;
    private BigDecimal totalAmount;
    private String userNickname;
    private String recipientName;
    private String recipientPhone;
    private Integer status;
    private LocalDateTime createTime;

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getOrderNo() { return orderNo; }
    public void setOrderNo(String orderNo) { this.orderNo = orderNo; }

    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }

    public String getUserNickname() { return userNickname; }
    public void setUserNickname(String userNickname) { this.userNickname = userNickname; }

    public String getRecipientName() { return recipientName; }
    public void setRecipientName(String recipientName) { this.recipientName = recipientName; }

    public String getRecipientPhone() { return recipientPhone; }
    public void setRecipientPhone(String recipientPhone) { this.recipientPhone = recipientPhone; }

    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }

    public LocalDateTime getCreateTime() { return createTime; }
    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
}

/**
 * 订单服务实现类
 */
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderItemMapper orderItemMapper;

    @Autowired
    private CartItemMapper cartItemMapper;

    @Autowired
    private FlowerMapper flowerMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserPickupInfoService userPickupInfoService;

    @Override
    @Transactional
    public Order createOrderFromCart(Long userId, String recipientName, String recipientPhone, 
                                   String recipientAddress, String deliveryNotes, String remark) {
        // 获取购物车商品
        LambdaQueryWrapper<CartItem> cartWrapper = new LambdaQueryWrapper<>();
        cartWrapper.eq(CartItem::getUserId, userId);
        List<CartItem> cartItems = cartItemMapper.selectList(cartWrapper);

        if (cartItems.isEmpty()) {
            throw new RuntimeException("购物车为空");
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setStatus(2); // 已下单（货到付款模式）
        order.setPaymentStatus(0); // 未支付
        order.setDeliveryStatus(0); // 未配送
        order.setRecipientName(recipientName);
        order.setRecipientPhone(recipientPhone);
        order.setRecipientAddress(recipientAddress);
        order.setDeliveryNotes(deliveryNotes);
        order.setRemark(remark);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        // 计算订单总金额
        for (CartItem cartItem : cartItems) {
            Flower flower = flowerMapper.selectById(cartItem.getFlowerId());
            if (flower != null && flower.getStatus() == 1) {
                BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(cartItem.getQuantity()));
                totalAmount = totalAmount.add(subtotal);
            }
        }

        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(BigDecimal.ZERO);
        order.setFinalAmount(totalAmount);

        orderMapper.insert(order);

        // 创建订单商品
        for (CartItem cartItem : cartItems) {
            Flower flower = flowerMapper.selectById(cartItem.getFlowerId());
            if (flower != null && flower.getStatus() == 1) {
                OrderItem orderItem = new OrderItem();
                orderItem.setOrderId(order.getId());
                orderItem.setFlowerId(flower.getId());
                orderItem.setFlowerName(flower.getName());
                orderItem.setFlowerImage(flower.getMainImage());
                orderItem.setPrice(flower.getPrice());
                orderItem.setQuantity(cartItem.getQuantity());
                orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(cartItem.getQuantity())));
                orderItem.setCreatedAt(LocalDateTime.now());

                orderItemMapper.insert(orderItem);
            }
        }

        // 清空购物车
        cartItemMapper.delete(cartWrapper);

        // 发送新订单通知
        try {
            User user = userMapper.selectById(userId);
            OrderNotificationData notificationData = new OrderNotificationData();
            notificationData.setId(order.getId());
            notificationData.setOrderNo(order.getOrderNo());
            notificationData.setTotalAmount(order.getTotalAmount());
            notificationData.setUserNickname(user != null ? user.getNickname() : "未知用户");
            notificationData.setRecipientName(order.getRecipientName());
            notificationData.setRecipientPhone(order.getRecipientPhone());
            notificationData.setStatus(order.getStatus());
            notificationData.setCreateTime(order.getCreatedAt());

            NotificationWebSocket.sendNewOrderNotification(notificationData);
        } catch (Exception e) {
            // 通知发送失败不影响订单创建
            System.err.println("发送新订单通知失败: " + e.getMessage());
        }

        return order;
    }

    @Override
    @Transactional
    public Order createOrder(Long userId, List<Long> flowerIds, List<Integer> quantities,
                           String recipientName, String recipientPhone, String recipientAddress, 
                           String deliveryNotes, String remark) {
        if (flowerIds.size() != quantities.size()) {
            throw new RuntimeException("Flower IDs and quantities size mismatch");
        }
        
        // Create order
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setStatus(2); // 已下单（货到付款模式）
        order.setPaymentStatus(0); // unpaid
        order.setDeliveryStatus(0); // 未配送
        order.setRecipientName(recipientName);
        order.setRecipientPhone(recipientPhone);
        order.setRecipientAddress(recipientAddress);
        order.setDeliveryNotes(deliveryNotes);
        order.setRemark(remark);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        // Calculate total amount
        for (int i = 0; i < flowerIds.size(); i++) {
            Flower flower = flowerMapper.selectById(flowerIds.get(i));
            if (flower != null && flower.getStatus() == 1) {
                BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(quantities.get(i)));
                totalAmount = totalAmount.add(subtotal);
            }
        }
        
        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(BigDecimal.ZERO);
        order.setFinalAmount(totalAmount);
        
        orderMapper.insert(order);
        
        // Create order items
        for (int i = 0; i < flowerIds.size(); i++) {
            Flower flower = flowerMapper.selectById(flowerIds.get(i));
            if (flower != null && flower.getStatus() == 1) {
                OrderItem orderItem = new OrderItem();
                orderItem.setOrderId(order.getId());
                orderItem.setFlowerId(flower.getId());
                orderItem.setFlowerName(flower.getName());
                orderItem.setFlowerImage(flower.getMainImage());
                orderItem.setPrice(flower.getPrice());
                orderItem.setQuantity(quantities.get(i));
                orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(quantities.get(i))));
                orderItem.setCreatedAt(LocalDateTime.now());
                
                orderItemMapper.insert(orderItem);
            }
        }
        
        return order;
    }

    @Override
    public Order getOrderById(Long orderId) {
        return orderMapper.selectById(orderId);
    }

    @Override
    public List<OrderItem> getOrderItems(Long orderId) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderItem::getOrderId, orderId);
        return orderItemMapper.selectList(wrapper);
    }

    @Override
    public PageResult<Order> getUserOrders(Long userId, Long current, Long size, Integer status) {
        Page<Order> page = new Page<>(current, size);
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        
        wrapper.eq(Order::getUserId, userId);
        if (status != null) {
            wrapper.eq(Order::getStatus, status);
        }
        wrapper.orderByDesc(Order::getCreatedAt);
        
        IPage<Order> result = orderMapper.selectPage(page, wrapper);
        return PageResult.of(result.getRecords(), result.getTotal(), result.getSize(), result.getCurrent());
    }

    @Override
    public Order updateOrderStatus(Long orderId, Integer status) {
        Order order = orderMapper.selectById(orderId);
        if (order != null) {
            order.setStatus(status);
            order.setUpdatedAt(LocalDateTime.now());

            // 管理端订单状态处理（货到付款模式）
            if (status == 1) { // 待付款（送达后等待付款）
                order.setDeliveredAt(LocalDateTime.now());
                order.setDeliveryStatus(2); // 已送达
                // 保持未支付状态，等待确认付款
            } else if (status == 2) { // 已下单
                // 保持原有的支付状态，默认为未支付
                if (order.getPaymentStatus() == null) {
                    order.setPaymentStatus(0); // 未支付
                }
            } else if (status == 3) { // 配送中
                order.setShippedAt(LocalDateTime.now());
                if (order.getDeliveryStatus() == null) {
                    order.setDeliveryStatus(1); // 配送中
                }
            } else if (status == 4) { // 已完成（用户确认收货后）
                // 如果paymentStatus还未设置为已支付，则设置为已支付
                if (order.getPaymentStatus() != 1) {
                    order.setPaymentStatus(1); // 已支付
                    order.setPaidAt(LocalDateTime.now());
                }
                order.setDeliveryStatus(3); // 已完成
            } else if (status == 5) { // 已取消
                // 取消订单
            }

            orderMapper.updateById(order);
        }
        return order;
    }

    @Override
    public Boolean cancelOrder(Long orderId, Long userId) {
        Order order = orderMapper.selectById(orderId);
        if (order != null && order.getUserId().equals(userId) && order.getStatus() == 1) {
            order.setStatus(5); // cancelled
            order.setUpdatedAt(LocalDateTime.now());
            orderMapper.updateById(order);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean payOrder(Long orderId, String paymentMethod) {
        Order order = orderMapper.selectById(orderId);
        if (order != null && order.getStatus() == 1 && order.getPaymentStatus() == 0) {
            order.setStatus(2); // paid
            order.setPaymentStatus(1); // paid
            order.setPaymentMethod(paymentMethod);
            order.setPaidAt(LocalDateTime.now());
            order.setUpdatedAt(LocalDateTime.now());
            orderMapper.updateById(order);
            
            // Update flower sales count
            List<OrderItem> orderItems = getOrderItems(orderId);
            for (OrderItem item : orderItems) {
                Flower flower = flowerMapper.selectById(item.getFlowerId());
                if (flower != null) {
                    flower.setSalesCount(flower.getSalesCount() + item.getQuantity());
                    flower.setStockQuantity(flower.getStockQuantity() - item.getQuantity());
                    flowerMapper.updateById(flower);
                }
            }
            
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public Order createDirectOrder(Long userId, List<Long> flowerIds, List<Integer> quantities,
                                  Integer deliveryType, String recipientName, String recipientPhone,
                                  String recipientAddress, String deliveryTime, String pickupName, String pickupPhone,
                                  String pickupTime, String remark, String backupAddress) {
        if (flowerIds.size() != quantities.size()) {
            throw new RuntimeException("商品ID和数量不匹配");
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setStatus(2); // 已下单（货到付款模式）
        order.setPaymentStatus(0); // 未支付（货到付款）
        order.setDeliveryStatus(0); // 未配送
        order.setDeliveryType(deliveryType);
        order.setRemark(remark);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());

        // 根据配送方式设置相关信息
        if (deliveryType == 1) {
            // 外卖配送
            order.setRecipientName(recipientName);
            order.setRecipientPhone(recipientPhone);
            order.setRecipientAddress(recipientAddress);
            order.setDeliveryTime(deliveryTime);
        } else {
            // 自取
            order.setPickupName(pickupName);
            order.setPickupPhone(pickupPhone);
            order.setPickupTime(pickupTime);
            if (backupAddress != null && !backupAddress.trim().isEmpty()) {
                order.setRecipientAddress(backupAddress); // 备用地址存储在收货地址字段
            }

            // 保存用户自取信息
            UserPickupInfo pickupInfo = new UserPickupInfo();
            pickupInfo.setUserId(userId);
            pickupInfo.setPickupName(pickupName);
            pickupInfo.setPickupPhone(pickupPhone);
            pickupInfo.setPickupTime(pickupTime);
            pickupInfo.setRemark(remark);
            pickupInfo.setBackupAddress(backupAddress);
            userPickupInfoService.saveOrUpdate(pickupInfo);
        }

        BigDecimal totalAmount = BigDecimal.ZERO;

        // 计算订单总金额并验证商品
        for (int i = 0; i < flowerIds.size(); i++) {
            Flower flower = flowerMapper.selectById(flowerIds.get(i));
            if (flower == null || flower.getStatus() != 1) {
                throw new RuntimeException("商品不存在或已下架");
            }
            if (flower.getStockQuantity() < quantities.get(i)) {
                throw new RuntimeException("商品库存不足：" + flower.getName());
            }
            BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(quantities.get(i)));
            totalAmount = totalAmount.add(subtotal);
        }

        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(BigDecimal.ZERO);
        order.setFinalAmount(totalAmount);

        orderMapper.insert(order);

        // 创建订单商品并更新库存
        for (int i = 0; i < flowerIds.size(); i++) {
            Flower flower = flowerMapper.selectById(flowerIds.get(i));
            if (flower != null && flower.getStatus() == 1) {
                OrderItem orderItem = new OrderItem();
                orderItem.setOrderId(order.getId());
                orderItem.setFlowerId(flower.getId());
                orderItem.setFlowerName(flower.getName());
                orderItem.setFlowerImage(flower.getMainImage());
                orderItem.setPrice(flower.getPrice());
                orderItem.setQuantity(quantities.get(i));
                orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(quantities.get(i))));
                orderItem.setCreatedAt(LocalDateTime.now());

                orderItemMapper.insert(orderItem);

                // 更新商品销量和库存
                flower.setSalesCount(flower.getSalesCount() + quantities.get(i));
                flower.setStockQuantity(flower.getStockQuantity() - quantities.get(i));
                flowerMapper.updateById(flower);
            }
        }

        return order;
    }

    @Override
    public Boolean confirmPayment(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order != null && order.getStatus() == 1 && order.getPaymentStatus() == 0) {
            // 只更新支付状态，保持订单状态为待付款，让用户可以确认收货
            order.setPaymentStatus(1); // 已支付
            order.setPaidAt(LocalDateTime.now());
            order.setUpdatedAt(LocalDateTime.now());
            orderMapper.updateById(order);
            return true;
        }
        return false;
    }

    @Override
    public Map<String, Integer> getOrderStatsByUserId(Long userId) {
        Map<String, Integer> stats = new HashMap<>();

        // 待付款：状态1且未支付
        LambdaQueryWrapper<Order> pendingWrapper = new LambdaQueryWrapper<>();
        pendingWrapper.eq(Order::getUserId, userId)
                     .eq(Order::getStatus, 1)
                     .eq(Order::getPaymentStatus, 0);
        int pendingCount = Math.toIntExact(orderMapper.selectCount(pendingWrapper));

        // 已下单：状态2
        LambdaQueryWrapper<Order> orderedWrapper = new LambdaQueryWrapper<>();
        orderedWrapper.eq(Order::getUserId, userId)
                     .eq(Order::getStatus, 2);
        int orderedCount = Math.toIntExact(orderMapper.selectCount(orderedWrapper));

        // 配送中：状态3
        LambdaQueryWrapper<Order> shippingWrapper = new LambdaQueryWrapper<>();
        shippingWrapper.eq(Order::getUserId, userId)
                      .eq(Order::getStatus, 3);
        int shippingCount = Math.toIntExact(orderMapper.selectCount(shippingWrapper));

        // 待收货：状态1且已支付
        LambdaQueryWrapper<Order> toReceiveWrapper = new LambdaQueryWrapper<>();
        toReceiveWrapper.eq(Order::getUserId, userId)
                       .eq(Order::getStatus, 1)
                       .eq(Order::getPaymentStatus, 1);
        int toReceiveCount = Math.toIntExact(orderMapper.selectCount(toReceiveWrapper));

        // 已完成：状态4
        LambdaQueryWrapper<Order> completedWrapper = new LambdaQueryWrapper<>();
        completedWrapper.eq(Order::getUserId, userId)
                       .eq(Order::getStatus, 4);
        int completedCount = Math.toIntExact(orderMapper.selectCount(completedWrapper));

        stats.put("pending", pendingCount);
        stats.put("ordered", orderedCount);
        stats.put("shipping", shippingCount);
        stats.put("toReceive", toReceiveCount);
        stats.put("completed", completedCount);

        return stats;
    }

    private String generateOrderNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = IdUtil.randomUUID().substring(0, 6).toUpperCase();
        return "FL" + timestamp + random;
    }
}
