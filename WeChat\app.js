// app.js
App({
  globalData: {
    userInfo: null,
    userId: null,
    openid: null,
    baseUrl: 'http://localhost:8080/api',
    cartCount: 0,
    cartBadgeCount: 0, // 徽章显示的数量（未读的新增商品）
    lastViewedCartTime: null, // 最后查看购物车的时间
    selectedCategoryId: null, // 从首页选择的分类ID
    // 订单数字提醒的已读状态
    orderBadgeReadStatus: {
      pending: 0,     // 待付款已读数量
      ordered: 0,     // 已下单已读数量
      shipping: 0,    // 配送中已读数量
      toReceive: 0,   // 待收货已读数量
      lastReadTime: Date.now()
    }
  },

  onLaunch() {
    console.log('App Launch')

    // 初始化订单徽章已读状态
    this.initOrderBadgeReadStatus()

    // 清除过期的已读状态
    this.clearExpiredReadStatus()

    this.checkLogin()
  },

  onShow() {
    console.log('App Show')
  },

  onHide() {
    console.log('App Hide')
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = wx.getStorageSync('userInfo')
    const openid = wx.getStorageSync('openid')

    if (userInfo && openid) {
      this.globalData.userInfo = userInfo
      this.globalData.openid = openid
      this.globalData.userId = userInfo.id
      this.initCartBadge()
    }
  },

  // 初始化购物车徽章
  initCartBadge() {
    // 获取上次查看购物车的时间
    const lastViewedTime = wx.getStorageSync('lastViewedCartTime') || 0
    this.globalData.lastViewedCartTime = lastViewedTime

    // 初始时不显示徽章，只有新添加商品时才显示
    this.globalData.cartBadgeCount = 0
    this.updateCartCount(false)
  },

  // 微信登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 发送 res.code 到后台换取 openId, sessionKey, unionId
            wx.request({
              url: `${this.globalData.baseUrl}/user/login`,
              method: 'POST',
              data: {
                code: res.code
              },
              success: (response) => {
                if (response.data.code === 200) {
                  const userInfo = response.data.data
                  this.globalData.userInfo = userInfo
                  this.globalData.openid = userInfo.openid
                  this.globalData.userId = userInfo.id
                  
                  // 存储到本地
                  wx.setStorageSync('userInfo', userInfo)
                  wx.setStorageSync('openid', userInfo.openid)
                  
                  this.updateCartCount()
                  resolve(userInfo)
                } else {
                  reject(response.data.message)
                }
              },
              fail: (error) => {
                reject(error)
              }
            })
          } else {
            reject('获取登录凭证失败')
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  },

  // 更新购物车数量
  updateCartCount(isAddingToCart = false) {
    if (!this.globalData.userId) return

    wx.request({
      url: `${this.globalData.baseUrl}/cart/count/${this.globalData.userId}`,
      method: 'GET',
      success: (res) => {
        if (res.data.code === 200) {
          const newCartCount = res.data.data || 0
          const oldCartCount = this.globalData.cartCount

          this.globalData.cartCount = newCartCount

          // 如果是添加到购物车的操作，增加徽章数量
          if (isAddingToCart && newCartCount > oldCartCount) {
            this.globalData.cartBadgeCount += (newCartCount - oldCartCount)
          }

          // 更新tabBar徽标
          this.updateTabBarBadge()
        }
      }
    })
  },

  // 更新TabBar徽章
  updateTabBarBadge() {
    if (this.globalData.cartBadgeCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: this.globalData.cartBadgeCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 清除购物车徽章（当用户查看购物车时调用）
  clearCartBadge() {
    this.globalData.cartBadgeCount = 0
    this.globalData.lastViewedCartTime = Date.now()

    // 保存查看时间到本地存储
    wx.setStorageSync('lastViewedCartTime', this.globalData.lastViewedCartTime)

    wx.removeTabBarBadge({
      index: 2
    })
  },

  // 添加到购物车时调用
  addToCartWithBadge() {
    this.updateCartCount(true)
  },

  // 缓存管理
  cacheManager: {
    // 设置缓存
    setCache(key, data, expireTime = 30 * 60 * 1000) { // 默认30分钟过期
      const cacheData = {
        data: data,
        timestamp: Date.now(),
        expireTime: expireTime
      }
      wx.setStorageSync(key, cacheData)
    },

    // 获取缓存
    getCache(key) {
      try {
        const cacheData = wx.getStorageSync(key)
        if (!cacheData) return null

        const now = Date.now()
        if (now - cacheData.timestamp > cacheData.expireTime) {
          wx.removeStorageSync(key)
          return null
        }

        return cacheData.data
      } catch (e) {
        return null
      }
    },

    // 清除指定缓存
    removeCache(key) {
      wx.removeStorageSync(key)
    },

    // 清除所有缓存
    clearAllCache() {
      try {
        wx.clearStorageSync()
        return true
      } catch (e) {
        return false
      }
    },

    // 清除过期缓存
    clearExpiredCache() {
      try {
        const info = wx.getStorageInfoSync()
        const now = Date.now()

        info.keys.forEach(key => {
          try {
            const cacheData = wx.getStorageSync(key)
            if (cacheData && cacheData.timestamp && cacheData.expireTime) {
              if (now - cacheData.timestamp > cacheData.expireTime) {
                wx.removeStorageSync(key)
              }
            }
          } catch (e) {
            // 忽略错误，继续清理其他缓存
          }
        })
        return true
      } catch (e) {
        return false
      }
    },

    // 获取缓存大小信息
    getCacheInfo() {
      try {
        const info = wx.getStorageInfoSync()
        return {
          keys: info.keys,
          currentSize: info.currentSize,
          limitSize: info.limitSize,
          keyCount: info.keys.length
        }
      } catch (e) {
        return {
          keys: [],
          currentSize: 0,
          limitSize: 0,
          keyCount: 0
        }
      }
    },

    // 清除特定类型的缓存
    clearCacheByType(type) {
      try {
        const info = wx.getStorageInfoSync()
        const keysToRemove = info.keys.filter(key => key.startsWith(type))

        keysToRemove.forEach(key => {
          wx.removeStorageSync(key)
        })

        return keysToRemove.length
      } catch (e) {
        return 0
      }
    }
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.globalData.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        success: (res) => {
          if (res.data.code === 200) {
            resolve(res.data)
          } else {
            wx.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        },
        fail: (error) => {
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          })
          reject(error)
        }
      })
    })
  },

  // 清理详细地址中的省市区信息（通用工具函数）
  cleanDetailedAddress(detailedAddress, province, city, district) {
    if (!detailedAddress) return ''

    let cleaned = detailedAddress.trim()

    // 构建可能的省市区组合模式
    const patterns = []

    if (province && city && district) {
      // 完整的省市区组合
      patterns.push(`${province}${city}${district}`)
      patterns.push(`${province} ${city} ${district}`)
    }

    if (province && city) {
      patterns.push(`${province}${city}`)
      patterns.push(`${province} ${city}`)
    }

    if (city && district) {
      patterns.push(`${city}${district}`)
      patterns.push(`${city} ${district}`)
    }

    // 单独的省市区
    if (province) patterns.push(province)
    if (city) patterns.push(city)
    if (district) patterns.push(district)

    // 按长度排序，优先移除长的匹配
    patterns.sort((a, b) => b.length - a.length)

    // 移除匹配的省市区信息
    for (const pattern of patterns) {
      if (cleaned.includes(pattern)) {
        cleaned = cleaned.replace(new RegExp(pattern, 'g'), '').trim()
      }
    }

    // 移除开头的常见分隔符
    cleaned = cleaned.replace(/^[\s,，。、-]+/, '')

    // 移除结尾的常见分隔符
    cleaned = cleaned.replace(/[\s,，。、-]+$/, '')

    // 清理多余的空格
    cleaned = cleaned.replace(/\s+/g, ' ').trim()

    return cleaned
  },

  // 格式化完整地址（确保省市区和详细地址不重复）
  formatFullAddress(province, city, district, detailedAddress) {
    const cleanedDetail = this.cleanDetailedAddress(detailedAddress, province, city, district)
    const parts = [province, city, district, cleanedDetail].filter(part => part && part.trim())
    return parts.join(' ')
  },

  // 初始化订单徽章已读状态（从本地存储加载）
  initOrderBadgeReadStatus() {
    try {
      const savedStatus = wx.getStorageSync('orderBadgeReadStatus')
      if (savedStatus) {
        this.globalData.orderBadgeReadStatus = {
          ...this.globalData.orderBadgeReadStatus,
          ...savedStatus
        }
      }
    } catch (e) {
      console.error('加载订单徽章已读状态失败', e)
    }
  },

  // 更新订单徽章已读状态
  updateOrderBadgeReadStatus(status, count) {
    this.globalData.orderBadgeReadStatus[status] = count || 0
    this.globalData.orderBadgeReadStatus.lastReadTime = Date.now()

    // 保存到本地存储
    try {
      wx.setStorageSync('orderBadgeReadStatus', this.globalData.orderBadgeReadStatus)
    } catch (e) {
      console.error('保存订单徽章已读状态失败', e)
    }

    // 触发全局事件，通知其他页面更新
    this.triggerGlobalEvent('orderBadgeUpdated', {
      status: status,
      readCount: count
    })
  },

  // 清除过期的已读状态（可选：7天后清除）
  clearExpiredReadStatus() {
    const now = Date.now()
    const expireTime = 7 * 24 * 60 * 60 * 1000 // 7天

    if (this.globalData.orderBadgeReadStatus.lastReadTime &&
        now - this.globalData.orderBadgeReadStatus.lastReadTime > expireTime) {
      this.globalData.orderBadgeReadStatus = {
        pending: 0,
        ordered: 0,
        shipping: 0,
        toReceive: 0,
        lastReadTime: now
      }

      try {
        wx.setStorageSync('orderBadgeReadStatus', this.globalData.orderBadgeReadStatus)
      } catch (e) {
        console.error('清除过期已读状态失败', e)
      }
    }
  },

  // 全局事件系统
  globalEventListeners: {},

  // 注册全局事件监听
  onGlobalEvent(eventName, callback) {
    if (!this.globalEventListeners[eventName]) {
      this.globalEventListeners[eventName] = []
    }
    this.globalEventListeners[eventName].push(callback)
  },

  // 移除全局事件监听
  offGlobalEvent(eventName, callback) {
    if (this.globalEventListeners[eventName]) {
      const index = this.globalEventListeners[eventName].indexOf(callback)
      if (index > -1) {
        this.globalEventListeners[eventName].splice(index, 1)
      }
    }
  },

  // 触发全局事件
  triggerGlobalEvent(eventName, data) {
    if (this.globalEventListeners[eventName]) {
      this.globalEventListeners[eventName].forEach(callback => {
        callback(data)
      })
    }
  }
})
