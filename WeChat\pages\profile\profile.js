// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    currentAvatarUrl: '/images/default-avatar.png',
    currentNickname: '微信用户',
    currentPhone: '点击绑定手机号',
    orderCounts: {
      pending: 0,
      paid: 0,
      shipped: 0,
      delivered: 0
    },
    cacheSize: '0KB',
    showCacheModal: false,
    cacheInfo: {
      keys: [],
      currentSize: 0,
      limitSize: 0,
      keyCount: 0,
      sizeText: '0MB'
    }
  },

  onLoad() {
    this.loadUserInfo()
    this.calculateCacheSize()
  },

  onShow() {
    this.loadUserInfo()
    this.loadOrderCounts()

    // 注册全局事件监听
    this.onOrderBadgeUpdated = (data) => {
      this.loadOrderCounts()
    }
    app.onGlobalEvent('orderBadgeUpdated', this.onOrderBadgeUpdated)

    // 启动定时刷新（每30秒刷新一次订单数量）
    this.startAutoRefresh()
  },

  onHide() {
    // 移除全局事件监听
    if (this.onOrderBadgeUpdated) {
      app.offGlobalEvent('orderBadgeUpdated', this.onOrderBadgeUpdated)
    }

    // 停止定时刷新
    this.stopAutoRefresh()
  },

  // 启动自动刷新
  startAutoRefresh() {
    this.stopAutoRefresh() // 先清除之前的定时器
    this.refreshTimer = setInterval(() => {
      this.loadOrderCounts()
    }, 30000) // 30秒刷新一次
  },

  // 停止自动刷新
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  },

  // 重置订单徽章已读状态（用于测试，长按头像触发）
  resetOrderBadgeReadStatus() {
    app.globalData.orderBadgeReadStatus = {
      pending: 0,
      ordered: 0,
      shipping: 0,
      toReceive: 0,
      lastReadTime: Date.now()
    }

    try {
      wx.setStorageSync('orderBadgeReadStatus', app.globalData.orderBadgeReadStatus)
      wx.showToast({
        title: '已重置徽章状态',
        icon: 'success'
      })
      this.loadOrderCounts()
    } catch (e) {
      console.error('重置徽章状态失败', e)
    }
  },

  onPullDownRefresh() {
    this.loadUserInfo()
    this.loadOrderCounts()
    wx.stopPullDownRefresh()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = app.globalData.userInfo
    console.log('个人页面加载用户信息:', userInfo)

    if (userInfo && userInfo.openid) {
      // 从服务器获取最新的用户信息
      this.fetchUserInfoFromServer(userInfo.openid)
    } else {
      // 使用本地缓存的用户信息
      this.setData({
        userInfo: userInfo,
        currentAvatarUrl: userInfo?.avatarUrl || '/images/default-avatar.png',
        currentNickname: userInfo?.nickname || '微信用户',
        currentPhone: userInfo?.phone || '点击绑定手机号'
      })
    }
  },

  // 从服务器获取用户信息
  fetchUserInfoFromServer(openid) {
    console.log('从服务器获取用户信息, openid:', openid)

    // 检查openid是否有效
    if (!openid || openid.trim() === '') {
      console.error('OpenID为空，无法获取用户信息')
      this.useLocalUserInfo()
      return
    }

    app.request({
      url: `/user/info/${openid}`,
      method: 'GET'
    }).then(response => {
      console.log('服务器返回的用户信息:', response)
      const serverUserInfo = response.data

      // 更新全局用户信息
      app.globalData.userInfo = { ...app.globalData.userInfo, ...serverUserInfo }

      // 更新页面显示
      // 检查头像URL是否有效，如果是服务器路径但可能不存在，则准备使用默认头像
      let avatarUrl = serverUserInfo?.avatarUrl || '/images/default-avatar.png'
      if (avatarUrl.includes('localhost:8080') && !avatarUrl.includes('default')) {
        console.log('使用服务器头像，如果加载失败会自动切换到默认头像')
      }

      this.setData({
        userInfo: serverUserInfo,
        currentAvatarUrl: avatarUrl,
        currentNickname: serverUserInfo?.nickname || '微信用户',
        currentPhone: serverUserInfo?.phone || '点击绑定手机号'
      })

      console.log('头像URL:', serverUserInfo?.avatarUrl)
    }).catch(err => {
      console.error('获取用户信息失败:', err)
      this.useLocalUserInfo()
    })
  },

  // 使用本地用户信息
  useLocalUserInfo() {
    const userInfo = app.globalData.userInfo
    console.log('使用本地用户信息:', userInfo)
    this.setData({
      userInfo: userInfo,
      currentAvatarUrl: userInfo?.avatarUrl || '/images/default-avatar.png',
      currentNickname: userInfo?.nickname || '微信用户',
      currentPhone: userInfo?.phone || '点击绑定手机号'
    })
  },

  // 头像加载失败处理
  onAvatarError(e) {
    console.log('头像加载失败:', e.detail)
    this.setData({
      currentAvatarUrl: '/images/default-avatar.png'
    })
  },

  // 加载订单统计
  loadOrderCounts() {
    if (!app.globalData.userId) return

    app.request({
      url: `/order/stats/${app.globalData.userId}`,
      method: 'GET'
    }).then(res => {
      const stats = res.data || {}

      // 根据新的订单流程映射数据
      const actualCounts = {
        pending: stats.pending || 0,      // 待付款（状态1且未支付）
        ordered: stats.ordered || 0,      // 已下单（状态2）
        shipping: stats.shipping || 0,    // 配送中（状态3）
        toReceive: stats.toReceive || 0,  // 待收货（状态1且已支付）
        completed: stats.completed || 0   // 已完成（状态4）
      }

      // 计算显示的数字（实际数量 - 已读数量）
      const readStatus = app.globalData.orderBadgeReadStatus
      const displayCounts = {
        pending: Math.max(0, actualCounts.pending - (readStatus.pending || 0)),
        ordered: Math.max(0, actualCounts.ordered - (readStatus.ordered || 0)),
        shipping: Math.max(0, actualCounts.shipping - (readStatus.shipping || 0)),
        toReceive: Math.max(0, actualCounts.toReceive - (readStatus.toReceive || 0)),
        completed: 0 // 已完成不显示数字
      }

      console.log('订单统计计算:', {
        actualCounts,
        readStatus,
        displayCounts
      })

      this.setData({
        orderCounts: displayCounts,
        actualOrderCounts: actualCounts // 保存实际数量，供跳转时使用
      })
    }).catch(err => {
      console.error('获取订单统计失败', err)
      // 设置默认值
      this.setData({
        orderCounts: {
          pending: 0,
          ordered: 0,
          shipping: 0,
          toReceive: 0,
          completed: 0
        },
        actualOrderCounts: {
          pending: 0,
          ordered: 0,
          shipping: 0,
          toReceive: 0,
          completed: 0
        }
      })
    })
  },

  // 计算缓存大小
  calculateCacheSize() {
    try {
      const storageInfo = wx.getStorageInfoSync()
      const sizeKB = Math.round(storageInfo.currentSize)
      let sizeText = sizeKB + 'KB'
      
      if (sizeKB > 1024) {
        const sizeMB = (sizeKB / 1024).toFixed(1)
        sizeText = sizeMB + 'MB'
      }
      
      this.setData({
        cacheSize: sizeText
      })
    } catch (err) {
      console.error('获取缓存大小失败', err)
    }
  },

  // 跳转到登录页面
  goToLogin() {
    if (!this.data.userInfo) {
      wx.navigateTo({
        url: '/pages/login/login'
      })
    }
  },

  // 编辑用户信息
  editUserInfo() {
    if (!this.data.userInfo) {
      this.goToLogin()
      return
    }

    wx.navigateTo({
      url: '/pages/user-edit/user-edit'
    })
  },

  // 跳转到调试页面
  goToDebug() {
    wx.navigateTo({
      url: '/pages/debug/debug'
    })
  },

  // 跳转到订单页面
  goToOrders(e) {
    if (!app.globalData.userId) {
      this.showLoginPrompt()
      return
    }

    const status = e.currentTarget.dataset.status
    let url = '/pages/order-list/order-list'

    if (status) {
      // 更新已读状态
      const actualCounts = this.data.actualOrderCounts || {}
      const actualCount = actualCounts[status] || 0
      app.updateOrderBadgeReadStatus(status, actualCount)

      // 根据状态映射到实际的订单状态
      const statusMap = {
        'pending': '1',
        'ordered': '2',
        'shipping': '3',
        'toReceive': '1', // 特殊处理：待收货是状态1且已支付
        'completed': '4'
      }

      const actualStatus = statusMap[status]
      if (status === 'toReceive') {
        url += `?status=${actualStatus}&paymentStatus=1`
      } else {
        url += `?status=${actualStatus}`
      }
    }

    wx.navigateTo({
      url: url
    })
  },

  // 跳转到收藏页面
  goToFavorites() {
    if (!app.globalData.userId) {
      this.showLoginPrompt()
      return
    }

    wx.navigateTo({
      url: '/pages/favorites/favorites'
    })
  },

  // 跳转到地址管理
  goToAddress() {
    if (!app.globalData.userId) {
      this.showLoginPrompt()
      return
    }

    wx.navigateTo({
      url: '/pages/address-list/address-list'
    })
  },

  // 客服中心
  goToService() {
    wx.showModal({
      title: '客服中心',
      content: '如有问题请联系客服\n电话：400-123-4567\n微信：flower_shop',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 关于我们
  goToAbout() {
    wx.showModal({
      title: '关于花语小铺',
      content: '花语小铺致力于为用户提供最优质的花卉产品和服务，让每一份美好都能传递到您的身边。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除所有缓存数据吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            wx.clearStorageSync()
            
            // 重新设置必要的数据
            if (app.globalData.userInfo) {
              wx.setStorageSync('userInfo', app.globalData.userInfo)
              wx.setStorageSync('openid', app.globalData.openid)
            }
            
            this.calculateCacheSize()
            
            wx.showToast({
              title: '缓存清除成功',
              icon: 'success'
            })
          } catch (err) {
            console.error('清除缓存失败', err)
            wx.showToast({
              title: '清除缓存失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户数据
          app.globalData.userInfo = null
          app.globalData.openid = null
          app.globalData.userId = null
          app.globalData.cartCount = 0
          
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('openid')
          
          // 清除购物车徽标
          wx.removeTabBarBadge({
            index: 2
          })
          
          this.setData({
            userInfo: null,
            orderCounts: {
              pending: 0,
              paid: 0,
              shipped: 0,
              delivered: 0
            }
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 跳转到地址管理
  goToAddresses() {
    if (!app.globalData.userId) {
      this.showLoginPrompt()
      return
    }
    wx.navigateTo({
      url: '/pages/address-list/address-list'
    })
  },

  // 显示缓存管理弹窗
  showCacheModal() {
    const cacheInfo = app.cacheManager.getCacheInfo()
    const sizeInMB = (cacheInfo.currentSize / 1024 / 1024).toFixed(2)

    this.setData({
      showCacheModal: true,
      cacheInfo: {
        ...cacheInfo,
        sizeText: `${sizeInMB}MB`
      }
    })
  },

  // 隐藏缓存管理弹窗
  hideCacheModal() {
    this.setData({
      showCacheModal: false
    })
  },

  // 清除所有缓存
  clearAllCache() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有缓存数据吗？这将删除所有本地存储的数据。',
      success: (res) => {
        if (res.confirm) {
          const success = app.cacheManager.clearAllCache()
          if (success) {
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            })
            this.hideCacheModal()
          } else {
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 清除图片缓存
  clearImageCache() {
    const count = app.cacheManager.clearCacheByType('image_')
    wx.showToast({
      title: `清除了${count}个图片缓存`,
      icon: 'success'
    })
    this.showCacheModal() // 刷新缓存信息
  },

  // 清除用户数据缓存
  clearUserDataCache() {
    const keys = ['userInfo', 'lastViewedCartTime', 'searchHistory']
    keys.forEach(key => {
      app.cacheManager.removeCache(key)
    })
    wx.showToast({
      title: '用户数据缓存已清除',
      icon: 'success'
    })
    this.showCacheModal() // 刷新缓存信息
  },

  // 清除过期缓存
  clearExpiredCache() {
    const success = app.cacheManager.clearExpiredCache()
    if (success) {
      wx.showToast({
        title: '过期缓存已清除',
        icon: 'success'
      })
      this.showCacheModal() // 刷新缓存信息
    } else {
      wx.showToast({
        title: '清除失败',
        icon: 'none'
      })
    }
  },

  // 显示登录提示
  showLoginPrompt() {
    wx.showModal({
      title: '提示',
      content: '请先登录后再进行操作',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }
      }
    })
  }
})
